/**
 * 从URL获取图片并创建File对象
 *
 * @param url - 图片的URL地址
 * @param imageName - 图片文件名称
 * @returns 返回包含图片数据的File对象
 * @throws 当URL无效或网络请求失败时抛出错误
 */
export async function getImageFileFromUrl(url: string, imageName: string): Promise<File> {
  try {
    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`获取图片失败: ${response.status} ${response.statusText}`)
    }

    const blob = await response.blob()
    return new File([blob], imageName, { type: blob.type || 'image/png' })
  }
  catch (error) {
    throw new Error(`创建图片文件失败: ${(error as Error).message}`)
  }
}

/**
 * 将base64字符串转换为File对象
 *
 * @param base64 - base64编码的字符串
 * @param filename - 文件名
 * @param type - 文件MIME类型，默认为'image/png'
 * @returns 返回File对象
 * @throws 当base64格式无效时抛出错误
 */
export function base64ToFile(base64: string, filename: string, type = 'image/png'): File {
  try {
    // 移除base64字符串中的头部信息(如果存在)
    const base64String = base64.includes('base64,') ? base64.split('base64,')[1] : base64

    // 将base64解码为二进制数据
    const byteCharacters = atob(base64String)
    const byteArrays = new Uint8Array(byteCharacters.length)

    for (let i = 0; i < byteCharacters.length; i++) {
      byteArrays[i] = byteCharacters.charCodeAt(i)
    }

    const blob = new Blob([byteArrays], { type })
    return new File([blob], filename, { type })
  }
  catch (error) {
    throw new Error(`base64转文件失败: ${(error as Error).message}`)
  }
}

/**
 * 将File或Blob对象转换为base64字符串
 *
 * @param file - 要转换的File或Blob对象
 * @returns 返回base64编码的字符串
 * @throws 当文件读取失败时抛出错误
 */
export function fileToBase64(file: File | Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = error => reject(new Error(`文件转base64失败: ${error}`))
  })
}

/**
 * 将图片URL转换为base64字符串
 *
 * @param url - 图片URL地址
 * @returns 返回base64编码的字符串
 * @throws 当URL无效或转换失败时抛出错误
 */
export async function imageUrlToBase64(url: string): Promise<string> {
  try {
    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`获取图片失败: ${response.status} ${response.statusText}`)
    }

    const blob = await response.blob()
    return await fileToBase64(blob)
  }
  catch (error) {
    throw new Error(`图片URL转base64失败: ${(error as Error).message}`)
  }
}
