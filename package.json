{"name": "soybean-admin", "type": "module", "version": "1.5.0", "description": "", "main": "dist-electron/main/index.js", "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "pnpm clean && vite build --mode dev && CSC_IDENTITY_AUTO_DISCOVERY=false electron-builder --publish=never", "build-electron": "pnpm clean && pnpm typecheck && vite build --mode prod && CSC_IDENTITY_AUTO_DISCOVERY=false electron-builder --publish=always", "build-electron:dev": "pnpm clean && vite build --mode dev && CSC_IDENTITY_AUTO_DISCOVERY=false electron-builder --publish=never", "build-electron:test": "pnpm clean && vite build --mode test && CSC_IDENTITY_AUTO_DISCOVERY=false electron-builder --publish=never", "build-electron:dir": "vite build --mode test && CSC_IDENTITY_AUTO_DISCOVERY=false electron-builder --dir", "build:wl": "pnpm clean && vite build --mode dev && CSC_IDENTITY_AUTO_DISCOVERY=false electron-builder -wl --publish=never", "build:docker": "cross-env BUILD_COMMAND=\"pnpm build:wl\" docker compose up --build", "clean": "rimraf dist dist-electron release", "build:test": "vite build --mode test", "dev": "vite --mode test", "dev:prod": "vite --mode prod", "lint:fix": "eslint . --fix", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "prepare": "husky", "commitlint": "commitlint --edit", "preinstall": "npx only-allow pnpm", "release": "commit-and-tag-version", "commit": "cz", "openapi2ts": "openapi2ts"}, "dependencies": {"@better-scroll/core": "2.5.1", "@iconify/vue": "4.3.0", "@microsoft/fetch-event-source": "^2.0.1", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@vueuse/core": "12.5.0", "@yw/fabric": "workspace:*", "clipboard": "2.0.11", "cropperjs": "^2.0.0", "dayjs": "1.11.13", "defu": "6.1.4", "echarts": "5.6.0", "electron-updater": "^6.6.2", "esdk-obs-browserjs": "^3.24.3", "json5": "2.2.3", "naive-ui": "2.41.0", "nprogress": "0.2.0", "pinia": "3.0.0", "tailwind-merge": "3.0.1", "vue": "3.5.13", "vue-draggable-plus": "0.6.0", "vue-router": "4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@antfu/eslint-config": "^2.27.3", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@elegant-router/vue": "0.3.8", "@iconify/json": "2.2.305", "@sa/uno-preset": "workspace:*", "@types/node": "22.13.1", "@types/nprogress": "0.2.3", "@umijs/openapi": "^1.13.0", "@unocss/eslint-config": "65.4.3", "@unocss/preset-icons": "65.4.3", "@unocss/preset-uno": "65.4.3", "@unocss/transformer-directives": "65.4.3", "@unocss/transformer-variant-group": "65.4.3", "@unocss/vite": "65.4.3", "@vitejs/plugin-vue": "5.2.1", "@vitejs/plugin-vue-jsx": "4.1.1", "commit-and-tag-version": "^12.5.0", "commitizen": "^4.3.1", "cz-customizable": "^7.4.0", "electron": "^32.0.0", "electron-builder": "^26.0.12", "electron-devtools-installer": "^4.0.0", "eslint": "9.12.0", "husky": "^9.1.7", "lint-staged": "15.4.3", "only-allow": "^1.2.1", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.84.0", "tsx": "4.19.2", "typescript": "5.7.3", "unplugin-auto-import": "^19.1.1", "unplugin-icons": "22.0.0", "unplugin-vue-components": "28.0.0", "vite": "6.1.0", "vite-plugin-checker": "^0.9.1", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.7.1", "vue-tsc": "2.2.10"}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}, "cz-customizable": {"config": ".cz-config.cjs"}}, "pnpm": {"patchedDependencies": {"vite-plugin-electron@0.29.0": "patches/<EMAIL>"}, "onlyBuiltDependencies": ["canvas", "electron", "electron-winstaller"]}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["pnpm lint:fix"]}, "volta": {"node": "22.15.0"}}