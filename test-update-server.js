#!/usr/bin/env node
/* eslint-disable no-console */

/**
 * 简单的更新测试服务器
 * 支持JSON格式返回，使用模拟SHA512值
 */

const express = require('express');
const cors = require('cors');
const app = express();

app.use(cors());

// 模拟SHA512值（128个字符）
const mockSHA512 = 'a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890ab';

// 获取当前应用版本（从package.json）
const fs = require('fs');
const path = require('path');

function getCurrentVersion() {
  try {
    const packagePath = path.join(__dirname, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf-8'));
    return packageJson.version || '1.0.0';
  } catch {
    return '1.0.0';
  }
}

// 生成新版本号（当前版本+0.1.0）
function getNewVersion() {
  const current = getCurrentVersion();
  const parts = current.split('.');
  const major = parseInt(parts[0]) || 1;
  const minor = parseInt(parts[1]) || 0;
  const patch = parseInt(parts[2]) || 0;
  
  // 增加minor版本
  return `${major}.${minor + 1}.${patch}`;
}

// macOS 更新检查端点 (JSON格式)
app.get('/updates/latest-mac.json', (req, res) => {
  const newVersion = getNewVersion();
  
  const updateInfo = {
    version: newVersion,
    files: [
      {
        url: `soybean-admin-${newVersion}.dmg`,
        sha512: mockSHA512,
        size: 150000000 // 150MB
      }
    ],
    path: `soybean-admin-${newVersion}.dmg`,
    sha512: mockSHA512,
    releaseDate: new Date().toISOString(),
    releaseNotes: `## 🎉 版本 ${newVersion} 更新内容

### ✨ 新功能
- 添加了自动更新功能
- 优化了用户界面体验
- 增强了系统稳定性
- 新增了更多个性化设置

### 🐛 修复
- 修复了已知的内存泄漏问题
- 解决了窗口焦点切换问题
- 优化了应用启动速度
- 修复了系统托盘显示异常

### 🔧 改进
- 提升了整体性能
- 优化了网络请求处理
- 改进了错误提示信息

**建议立即更新以获得最佳体验！**`
  };
  
  console.log(`返回更新信息: ${newVersion} (当前版本: ${getCurrentVersion()})`);
  res.json(updateInfo);
});

// Windows 更新检查端点 (JSON格式)
app.get('/updates/latest.json', (req, res) => {
  const newVersion = getNewVersion();
  
  const updateInfo = {
    version: newVersion,
    files: [
      {
        url: `soybean-admin-${newVersion}.exe`,
        sha512: mockSHA512,
        size: 120000000 // 120MB
      }
    ],
    path: `soybean-admin-${newVersion}.exe`,
    sha512: mockSHA512,
    releaseDate: new Date().toISOString(),
    releaseNotes: `新版本 ${newVersion} 包含重要更新和修复`
  };
  
  console.log(`返回Windows更新信息: ${newVersion}`);
  res.json(updateInfo);
});

// 兼容YAML格式（如果需要）
app.get('/updates/latest-mac.yml', (req, res) => {
  res.redirect('/updates/latest-mac.json');
});

app.get('/updates/latest.yml', (req, res) => {
  res.redirect('/updates/latest.json');
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    currentVersion: getCurrentVersion(),
    newVersion: getNewVersion()
  });
});

// 启动服务器
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log('🚀 更新测试服务器启动成功！');
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🍎 macOS检查: http://localhost:${PORT}/updates/latest-mac.json`);
  console.log(`🪟 Windows检查: http://localhost:${PORT}/updates/latest.json`);
  console.log(`❤️  健康检查: http://localhost:${PORT}/health`);
  console.log(`📦 当前版本: ${getCurrentVersion()}`);
  console.log(`🆕 模拟新版本: ${getNewVersion()}`);
  console.log('');
  console.log('💡 提示: SHA512使用模拟值，仅用于测试更新逻辑');
});
