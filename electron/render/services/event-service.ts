import type { RendererIPC } from '../../ipc'
import { BaseRendererService } from './base-service'

/**
 * 渲染进程事件服务
 * 负责处理事件监听和相关的IPC通信
 */
export class EventService extends BaseRendererService {
  protected static serviceName = 'EventService'

  /**
   * 注册移除加载动画事件监听器
   * 监听主进程发送的移除加载动画消息，并执行相应的处理逻辑
   * @param rendererIPC 渲染进程 IPC 实例
   * @param removeLoadingCallback 移除加载动画的回调函数
   */
  static registerRemoveLoadingListener(
    rendererIPC: RendererIPC,
    removeLoadingCallback: () => void,
  ): void {
    this.logDebug('注册移除加载动画事件监听器', 'registerRemoveLoadingListener')

    rendererIPC.on('app:remove-loading', (_data: { timestamp: number }) => {
      this.logDebug('收到移除加载动画事件', 'registerRemoveLoadingListener')
      removeLoadingCallback()
    })
  }
}
