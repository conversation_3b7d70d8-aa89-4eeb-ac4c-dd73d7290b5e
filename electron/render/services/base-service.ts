/**
 * 渲染进程基础服务类
 * 提供通用的服务功能和错误处理
 */
export abstract class BaseRendererService {
  protected static serviceName: string = 'BaseRendererService'

  /**
   * 获取渲染进程IPC实例
   */
  protected static getIPC(): typeof window.optimizedIPC {
    if (typeof window === 'undefined' || !window.optimizedIPC) {
      throw new Error('IPC not available in current context')
    }
    return window.optimizedIPC
  }

  /**
   * 安全执行IPC调用
   */
  protected static async safeInvoke<TArgs, TResult>(
    channel: string,
    args?: TArgs,
  ): Promise<TResult> {
    try {
      const ipc = this.getIPC()
      this.logDebug(`准备调用IPC通道: ${channel}`, 'safeInvoke')

      // 如果没有参数，传递 null；否则传递实际参数
      const result = await ipc.invoke<TArgs | null, TResult>(channel, args ?? null)
      this.logDebug(`IPC调用成功: ${channel}`, 'safeInvoke')
      return result
    }
    catch (error) {
      this.logError(`IPC调用失败 [${channel}]: ${(error as any)?.message || error}`, 'safeInvoke')
      throw error
    }
  }

  /**
   * 记录调试信息
   */
  protected static logDebug(message: string, method: string): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[${this.serviceName}.${method}] ${message}`)
    }
  }

  /**
   * 记录错误信息
   */
  protected static logError(error: Error | string, method: string): void {
    console.error(`[${this.serviceName}.${method}] 错误:`, error)
  }
}
