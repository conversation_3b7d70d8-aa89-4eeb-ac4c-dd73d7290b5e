import type { ServiceResult } from '../../main/services/base-service'
import type { CpuInfo, MemoryUsage, SystemInfo } from '../../types/system-service'
import { BaseRendererService } from './base-service'

/**
 * 渲染进程系统服务
 * 负责与主进程的系统功能进行通信
 */
export class SystemService extends BaseRendererService {
  protected static serviceName = 'SystemService'

  /**
   * 获取完整系统信息
   * @returns Promise<ServiceResult<SystemInfo>>
   */
  static async getSystemInfo(): Promise<ServiceResult<SystemInfo>> {
    this.logDebug('开始获取系统信息', 'getSystemInfo')

    try {
      const result = await this.safeInvoke<void, ServiceResult<SystemInfo>>('system:getSystemInfo')
      this.logDebug('系统信息获取成功', 'getSystemInfo')
      return result
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'getSystemInfo')
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取系统信息失败',
      }
    }
  }

  /**
   * 获取内存使用情况
   * @returns Promise<ServiceResult<MemoryUsage>>
   */
  static async getMemoryUsage(): Promise<ServiceResult<MemoryUsage>> {
    this.logDebug('开始获取内存使用情况', 'getMemoryUsage')

    try {
      const result = await this.safeInvoke<void, ServiceResult<MemoryUsage>>('system:getMemoryUsage')
      this.logDebug('内存使用情况获取成功', 'getMemoryUsage')
      return result
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'getMemoryUsage')
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取内存使用情况失败',
      }
    }
  }

  /**
   * 获取CPU信息
   * @returns Promise<ServiceResult<CpuInfo>>
   */
  static async getCpuInfo(): Promise<ServiceResult<CpuInfo>> {
    this.logDebug('开始获取CPU信息', 'getCpuInfo')

    try {
      const result = await this.safeInvoke<void, ServiceResult<CpuInfo>>('system:getCpuInfo')
      this.logDebug('CPU信息获取成功', 'getCpuInfo')
      return result
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'getCpuInfo')
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取CPU信息失败',
      }
    }
  }

  /**
   * 获取网络接口信息
   * @returns Promise<ServiceResult<{ interfaces: Record<string, any[]> }>>
   */
  static async getNetworkInterfaces(): Promise<ServiceResult<{ interfaces: Record<string, any[]> }>> {
    this.logDebug('开始获取网络接口信息', 'getNetworkInterfaces')

    try {
      const result = await this.safeInvoke<void, ServiceResult<{ interfaces: Record<string, any[]> }>>('system:getNetworkInterfaces')
      this.logDebug('网络接口信息获取成功', 'getNetworkInterfaces')
      return result
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'getNetworkInterfaces')
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取网络接口信息失败',
      }
    }
  }
}
