import type { ServiceResult } from '../../main/services/base-service'
import type { WindowCreateOptions } from '../../types/window-service'
import { BaseRendererService } from './base-service'

/**
 * 渲染进程窗口服务
 * 负责与主进程的窗口功能进行通信
 */
export class WindowService extends BaseRendererService {
  protected static serviceName = 'WindowService'

  /**
   * 创建新窗口
   * @param options 窗口创建选项
   * @returns Promise<ServiceResult<{ windowId: number }>>
   */
  static async createWindow(options: WindowCreateOptions): Promise<ServiceResult<{ windowId: number }>> {
    this.logDebug('开始创建新窗口', 'createWindow')

    try {
      const result = await this.safeInvoke<WindowCreateOptions, ServiceResult<{ windowId: number }>>('window:create', options)
      this.logDebug(`窗口创建成功，ID: ${result.data?.windowId}`, 'createWindow')
      return result
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'createWindow')
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建窗口失败',
      }
    }
  }

  /**
   * 创建指定路由的窗口
   * @param options 额外的窗口选项
   * @returns Promise<ServiceResult<{ windowId: number }>>
   */
  static async createSettingsWindow(options: Partial<WindowCreateOptions> = {}): Promise<ServiceResult<{ windowId: number }>> {
    return this.createWindow({
      width: 800,
      height: 600,
      title: '设置',
      route: '/manage/user-detail/1', // 示例路由
      ...options,
    })
  }

  /**
   * 创建模态对话框窗口
   * @param parentWindowId 父窗口ID
   * @param route 要显示的路由
   * @param options 额外的窗口选项
   * @returns Promise<ServiceResult<{ windowId: number }>>
   */
  static async createModalWindow(
    parentWindowId: number,
    route: string,
    options: Partial<WindowCreateOptions> = {},
  ): Promise<ServiceResult<{ windowId: number }>> {
    return this.createWindow({
      width: 600,
      height: 400,
      modal: true,
      parent: parentWindowId,
      route,
      title: '对话框',
      ...options,
    })
  }
}
