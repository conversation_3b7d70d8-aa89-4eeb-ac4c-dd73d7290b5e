import type { ServiceResult } from '../../main/services/base-service'
import type { AppInfo } from '../../types/app-service'
import { BaseRendererService } from './base-service'

/**
 * 渲染进程应用服务
 * 负责与主进程的应用功能进行通信
 */
export class AppService extends BaseRendererService {
  protected static serviceName = 'AppService'

  /**
   * 获取完整应用信息
   * @returns Promise<ServiceResult<AppInfo>>
   */
  static async getAppInfo(): Promise<ServiceResult<AppInfo>> {
    this.logDebug('开始获取应用信息', 'getAppInfo')

    try {
      const result = await this.safeInvoke<void, ServiceResult<AppInfo>>('app:getInfo')
      this.logDebug('应用信息获取成功', 'getAppInfo')
      return result
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'getAppInfo')
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取应用信息失败',
      }
    }
  }

  /**
   * 退出应用
   * @returns Promise<ServiceResult<{ success: boolean }>>
   */
  static async quitApp(): Promise<ServiceResult<{ success: boolean }>> {
    this.logDebug('开始退出应用', 'quitApp')

    try {
      const result = await this.safeInvoke<void, ServiceResult<{ success: boolean }>>('app:quit')
      this.logDebug('应用退出成功', 'quitApp')
      return result
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'quitApp')
      return {
        success: false,
        error: error instanceof Error ? error.message : '退出应用失败',
      }
    }
  }

  /**
   * 重启应用
   * @returns Promise<ServiceResult<{ success: boolean }>>
   */
  static async relaunchApp(): Promise<ServiceResult<{ success: boolean }>> {
    this.logDebug('开始重启应用', 'relaunchApp')

    try {
      const result = await this.safeInvoke<void, ServiceResult<{ success: boolean }>>('app:relaunch')
      this.logDebug('应用重启成功', 'relaunchApp')
      return result
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'relaunchApp')
      return {
        success: false,
        error: error instanceof Error ? error.message : '重启应用失败',
      }
    }
  }

  /**
   * 获取应用路径
   * @param name 路径名称
   * @returns Promise<ServiceResult<{ path: string }>>
   */
  static async getPath(name: 'home' | 'appData' | 'userData' | 'cache' | 'temp' | 'exe' | 'module' | 'desktop' | 'documents' | 'downloads' | 'music' | 'pictures' | 'videos' | 'recent' | 'logs' | 'crashDumps'): Promise<ServiceResult<{ path: string }>> {
    this.logDebug(`开始获取应用路径: ${name}`, 'getPath')

    try {
      const result = await this.safeInvoke<string, ServiceResult<{ path: string }>>('app:getPath', name)
      this.logDebug(`应用路径获取成功: ${result.data?.path}`, 'getPath')
      return result
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'getPath')
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取应用路径失败',
      }
    }
  }
}
