import { BaseRendererService } from './base-service'

/**
 * 截图源接口
 */
interface ScreenshotSource {
  /** 源名称 */
  name: string
  /** 源ID */
  id: string
  /** 缩略图（base64格式） */
  thumbnail: string
  /** 显示器ID */
  display_id: string
  /** 应用图标（base64格式） */
  appIcon: string | null
  /** 源类型 */
  type: 'window' | 'screen'
}
/**
 * 截图操作结果
 */
interface ScreenshotResult {
  success: boolean
  error?: string
  data?: {
    sources: ScreenshotSource[]
  }
}

/**
 * 渲染进程截图服务
 * 负责与主进程的截图功能进行通信
 */
export class ScreenshotService extends BaseRendererService {
  protected static serviceName = 'ScreenshotService'

  /**
   * 获取截图源
   * @param windowId 窗口ID，默认为1（主窗口）
   * @returns Promise<ScreenshotResult>
   */
  static async getSources(windowId: number = 1): Promise<ScreenshotResult> {
    this.logDebug(`开始获取截图源，windowId: ${windowId}`, 'getSources')

    try {
      const result = await this.safeInvoke<number, ScreenshotResult>('window:screenshot', windowId)
      this.logDebug(`成功获取到 ${result.data?.sources?.length || 0} 个截图源`, 'getSources')
      return result
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'getSources')
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取截图源失败',
        data: { sources: [] },
      }
    }
  }
}
