import { fileURLToPath } from 'node:url'
import path from 'node:path'
import os from 'node:os'
import fs from 'node:fs'
import { app } from 'electron'

/**
 * 配置管理器
 * 集中管理所有路径配置、平台配置和应用设置
 */

const __dirname = path.dirname(fileURLToPath(import.meta.url))

/**
 * 获取应用根目录
 * 在开发环境中，返回项目根目录
 * 在打包环境中，返回 app.asar 的父目录
 */
function getAppRoot(): string {
  if (process.env.VITE_DEV_SERVER_URL) {
    // 开发环境：返回项目根目录
    // __dirname 在开发环境中指向 dist-electron/main，需要回到项目根目录
    return path.join(__dirname, '../..')
  }
  else {
    // 打包环境：返回 app.asar 所在目录
    return path.dirname(app.getAppPath())
  }
}

/**
 * 获取渲染进程文件目录
 * 在开发环境中，指向项目的 dist 目录
 * 在打包环境中，指向 app.asar 内的 dist 目录
 */
function getRendererDist(): string {
  if (process.env.VITE_DEV_SERVER_URL) {
    // 开发环境：项目根目录下的 dist
    return path.join(getAppRoot(), 'dist')
  }
  else {
    // 打包环境：app.asar 内的 dist 目录
    return path.join(app.getAppPath(), 'dist')
  }
}

// 路径配置
export const APP_PATHS = {
  get APP_ROOT() { return getAppRoot() },
  get MAIN_DIST() { return path.join(this.APP_ROOT, 'dist-electron') },
  get RENDERER_DIST() { return getRendererDist() },
  get PRELOAD() { return path.join(__dirname, '../preload/index.mjs') },
  get INDEX_HTML() { return path.join(this.RENDERER_DIST, 'index.html') },
  get VITE_PUBLIC() {
    return process.env.VITE_DEV_SERVER_URL
      ? path.join(this.APP_ROOT, 'public')
      : this.RENDERER_DIST
  },
} as const

// 开发服务器配置
export const DEV_CONFIG = {
  VITE_DEV_SERVER_URL: process.env.VITE_DEV_SERVER_URL,
  get IS_DEV() { return Boolean(this.VITE_DEV_SERVER_URL) },
} as const

// 平台配置
export const PLATFORM_CONFIG = {
  IS_WINDOWS: process.platform === 'win32',
  IS_MACOS: process.platform === 'darwin',
  IS_LINUX: process.platform === 'linux',
  IS_WINDOWS_7: os.release().startsWith('6.1'),
  get NEEDS_HARDWARE_ACCELERATION_DISABLED() { return this.IS_WINDOWS_7 },
} as const

/**
 * 应用配置接口
 */
export interface AppConfig {
  /** 是否启用单实例模式 */
  singleInstance: boolean
  /** 是否启用硬件加速 */
  hardwareAcceleration: boolean
  /** 应用用户模型ID (Windows) */
  appUserModelId?: string
  /** 是否启用开发者工具 */
  enableDevTools: boolean
}

/**
 * 窗口加载选项
 */
export interface WindowLoadOptions {
  preload: string
  indexHtml: string
  VITE_DEV_SERVER_URL?: string
}

/**
 * 配置验证结果
 */
export interface ConfigValidationResult {
  isValid: boolean
  errors: string[]
}

/**
 * 配置管理器类
 */
export class ConfigManager {
  private appConfig: AppConfig
  private isInitialized = false

  constructor() {
    this.appConfig = this.getDefaultConfig()
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): AppConfig {
    return {
      singleInstance: true,
      hardwareAcceleration: !PLATFORM_CONFIG.NEEDS_HARDWARE_ACCELERATION_DISABLED,
      appUserModelId: PLATFORM_CONFIG.IS_WINDOWS ? app.getName() : undefined,
      enableDevTools: DEV_CONFIG.IS_DEV,
    }
  }

  /**
   * 初始化配置管理器
   */
  initialize(customConfig: Partial<AppConfig> = {}): void {
    if (this.isInitialized) {
      return
    }

    // 合并自定义配置
    this.appConfig = { ...this.appConfig, ...customConfig }

    // 设置环境变量
    this.setupEnvironmentVariables()

    // 应用平台特定配置
    this.applyPlatformSpecificConfig()

    // 处理单实例检查
    this.handleSingleInstance()

    this.isInitialized = true
  }

  /**
   * 设置环境变量
   */
  private setupEnvironmentVariables(): void {
    process.env.APP_ROOT = APP_PATHS.APP_ROOT
    process.env.VITE_PUBLIC = APP_PATHS.VITE_PUBLIC
  }

  /**
   * 应用平台特定配置
   */
  private applyPlatformSpecificConfig(): void {
    // 硬件加速设置
    if (!this.appConfig.hardwareAcceleration) {
      app.disableHardwareAcceleration()
    }

    // Windows 应用用户模型ID
    if (this.appConfig.appUserModelId && PLATFORM_CONFIG.IS_WINDOWS) {
      app.setAppUserModelId(this.appConfig.appUserModelId)
    }
  }

  /**
   * 处理单实例检查
   */
  private handleSingleInstance(): void {
    if (this.appConfig.singleInstance && !app.requestSingleInstanceLock()) {
      app.quit()
      process.exit(0)
    }
  }

  /**
   * 验证配置的有效性
   */
  validate(): ConfigValidationResult {
    const errors: string[] = []

    try {
      // 检查关键路径是否存在
      if (!fs.existsSync(APP_PATHS.APP_ROOT)) {
        errors.push(`应用根目录不存在: ${APP_PATHS.APP_ROOT}`)
      }

      if (!fs.existsSync(APP_PATHS.PRELOAD)) {
        errors.push(`预加载脚本不存在: ${APP_PATHS.PRELOAD}`)
      }

      if (!DEV_CONFIG.IS_DEV && !fs.existsSync(APP_PATHS.INDEX_HTML)) {
        errors.push(`渲染进程入口文件不存在: ${APP_PATHS.INDEX_HTML}`)
      }
    }
    catch (error) {
      errors.push(`配置验证失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  /**
   * 获取窗口加载选项
   */
  getWindowLoadOptions(): WindowLoadOptions {
    return {
      preload: APP_PATHS.PRELOAD,
      indexHtml: APP_PATHS.INDEX_HTML,
      VITE_DEV_SERVER_URL: DEV_CONFIG.VITE_DEV_SERVER_URL,
    }
  }

  /**
   * 获取应用配置
   */
  getAppConfig(): AppConfig {
    return { ...this.appConfig }
  }

  /**
   * 获取路径配置
   */
  getAppPaths() {
    return APP_PATHS
  }

  /**
   * 获取开发配置
   */
  getDevConfig() {
    return DEV_CONFIG
  }

  /**
   * 获取平台配置
   */
  getPlatformConfig() {
    return PLATFORM_CONFIG
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<AppConfig>): void {
    this.appConfig = { ...this.appConfig, ...config }
  }

  /**
   * 重置为默认配置
   */
  resetToDefault(): void {
    this.appConfig = this.getDefaultConfig()
  }
}
