import type { BrowserWindow } from 'electron'
import { app } from 'electron'
import { WindowManager } from '../ui/window-manager'
import { moduleErrorHandlers } from '../core/error-handler'

/**
 * 事件管理器选项
 */
export interface EventManagerOptions {
  getMainWindow: () => BrowserWindow | null
  setMainWindow: (window: BrowserWindow | null) => void
  cleanup: () => void
}

/**
 * 事件清理函数类型
 */
type EventCleanupFunction = () => void

/**
 * 事件管理器
 * 负责管理所有应用程序级别的事件监听器
 */
export class EventManager {
  private eventCleanups: EventCleanupFunction[] = []
  private windowManager: WindowManager
  private isSetup = false

  constructor() {
    this.windowManager = new WindowManager()
  }

  /**
   * 设置事件监听器
   */
  setup(options: EventManagerOptions): void {
    if (this.isSetup) {
      moduleErrorHandlers.main.warning('事件管理器已经设置，跳过重复设置', 'setup')
      return
    }

    try {
      this.setupAppEvents(options)
      this.setupProcessEvents()
      this.isSetup = true

      moduleErrorHandlers.main.info('事件管理器设置完成', 'setup')
    }
    catch (error) {
      moduleErrorHandlers.main.error(
        error instanceof Error ? error : String(error),
        'setup',
      )
    }
  }

  /**
   * 设置应用程序事件
   */
  private setupAppEvents(options: EventManagerOptions): void {
    const { getMainWindow, setMainWindow, cleanup } = options

    // 应用退出前事件
    this.addEventHandler('before-quit', () => {
      moduleErrorHandlers.main.info('应用程序准备退出', 'before-quit')
      cleanup()
    })

    // 所有窗口关闭事件
    this.addEventHandler('window-all-closed', () => {
      this.windowManager.handleAllWindowsClosed(setMainWindow, cleanup)
    })

    // 第二个实例启动事件
    this.addEventHandler('second-instance', () => {
      this.windowManager.handleSecondInstance(getMainWindow)
    })

    // 应用程序隐藏事件 (macOS)
    this.addEventHandler('hide', () => {
      moduleErrorHandlers.main.info('应用程序已隐藏', 'hide')
    })

    // 应用程序显示事件 (macOS)
    this.addEventHandler('show', () => {
      moduleErrorHandlers.main.info('应用程序已显示', 'show')
    })

    // 证书错误事件
    this.addEventHandler('certificate-error', (event: any, _webContents: any, _url: string, _error: any, _certificate: any, callback: (trust: boolean) => void) => {
      // 在开发环境中允许自签名证书
      if (process.env.NODE_ENV === 'development') {
        event.preventDefault()
        callback(true)
      }
      else {
        callback(false)
      }
    })

    // 权限请求事件
    this.addEventHandler('web-contents-created', (_event: any, contents: any) => {
      contents.on('new-window', (event: any, navigationUrl: string) => {
        const parsedUrl = new URL(navigationUrl)

        // 只允许打开 HTTPS 链接
        if (parsedUrl.protocol !== 'https:') {
          event.preventDefault()
        }
      })
    })
  }

  /**
   * 设置进程事件
   */
  private setupProcessEvents(): void {
    // 处理未捕获的异常
    const uncaughtExceptionHandler = (error: Error) => {
      moduleErrorHandlers.main.fatal(error, 'uncaughtException')
    }
    process.on('uncaughtException', uncaughtExceptionHandler)
    this.eventCleanups.push(() => {
      try {
        (process as any).removeListener('uncaughtException', uncaughtExceptionHandler)
      }
      catch {
        // 忽略移除监听器时的错误
      }
    })

    // 处理未处理的 Promise 拒绝
    const unhandledRejectionHandler = (reason: any) => {
      moduleErrorHandlers.main.fatal(
        reason instanceof Error ? reason : String(reason),
        'unhandledRejection',
      )
    }
    process.on('unhandledRejection', unhandledRejectionHandler)
    this.eventCleanups.push(() => {
      try {
        (process as any).removeListener('unhandledRejection', unhandledRejectionHandler)
      }
      catch {
        // 忽略移除监听器时的错误
      }
    })

    // 处理 SIGINT 信号 (Ctrl+C)
    const sigintHandler = () => {
      moduleErrorHandlers.main.info('收到 SIGINT 信号，正在退出应用程序', 'sigint')
      app.quit()
    }
    process.on('SIGINT', sigintHandler)
    this.eventCleanups.push(() => {
      try {
        (process as any).removeListener('SIGINT', sigintHandler)
      }
      catch {
        // 忽略移除监听器时的错误
      }
    })

    // 处理 SIGTERM 信号
    const sigtermHandler = () => {
      moduleErrorHandlers.main.info('收到 SIGTERM 信号，正在退出应用程序', 'sigterm')
      app.quit()
    }
    process.on('SIGTERM', sigtermHandler)
    this.eventCleanups.push(() => {
      try {
        (process as any).removeListener('SIGTERM', sigtermHandler)
      }
      catch {
        // 忽略移除监听器时的错误
      }
    })
  }

  /**
   * 添加事件处理器的辅助方法
   */
  private addEventHandler(eventName: string, handler: (...args: any[]) => void): void {
    app.on(eventName as any, handler)
    this.eventCleanups.push(() => app.off(eventName as any, handler))
  }

  /**
   * 清理所有事件监听器
   */
  cleanup(): void {
    if (!this.isSetup) {
      return
    }

    try {
      this.eventCleanups.forEach((cleanup, index) => {
        try {
          cleanup()
        }
        catch (error) {
          moduleErrorHandlers.main.error(
            `清理事件监听器 ${index} 失败: ${error instanceof Error ? error.message : String(error)}`,
            'cleanup',
          )
        }
      })

      this.eventCleanups = []
      this.isSetup = false

      moduleErrorHandlers.main.info('事件管理器清理完成', 'cleanup')
    }
    catch (error) {
      moduleErrorHandlers.main.error(
        error instanceof Error ? error : String(error),
        'cleanup',
      )
    }
  }

  /**
   * 获取当前注册的事件数量
   */
  getEventCount(): number {
    return this.eventCleanups.length
  }

  /**
   * 检查是否已设置
   */
  isSetupComplete(): boolean {
    return this.isSetup
  }
}
