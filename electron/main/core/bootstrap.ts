import type { BrowserWindow, Tray } from 'electron'
import { mainIPC } from '../../ipc'
import { MenuManager } from '../ui/menu-manager'
import { TrayManager } from '../ui/tray-manager'
import { WindowManager } from '../ui/window-manager'
import { EventManager } from '../events/event-manager'
import { ConfigManager } from '../config/config-manager'
import { IPCManager } from '../ipc/ipc-manager'
import { AppEvents } from '../events'
import { UpdateService } from '../services/update-service'
import { moduleErrorHandlers } from './error-handler'
/**
 * 应用程序启动引导类
 * 负责协调各个模块的初始化和生命周期管理
 */
export class ApplicationBootstrap {
  private configManager: ConfigManager
  private windowManager: WindowManager
  private menuManager: MenuManager
  private trayManager: TrayManager
  private eventManager: EventManager
  private ipcManager: IPCManager

  private mainWindow: BrowserWindow | null = null
  private systemTray: Tray | null = null

  constructor() {
    // 初始化各个管理器
    this.configManager = new ConfigManager()
    this.windowManager = new WindowManager()
    this.menuManager = new MenuManager()
    this.trayManager = new TrayManager()
    this.eventManager = new EventManager()
    this.ipcManager = new IPCManager()
  }

  /**
   * 初始化应用程序
   */
  async initialize(): Promise<void> {
    try {
      // 1. 初始化配置
      await this.initializeConfig()

      // 2. 初始化 IPC 系统
      await this.initializeIPC()

      // 3. 创建主窗口
      await this.createMainWindow()

      // 4. 初始化 UI 组件
      await this.initializeUI()

      // 5. 设置事件监听器
      await this.setupEventListeners()

      // 6. 初始化更新服务
      await this.initializeUpdateService()

      moduleErrorHandlers.main.info('应用程序初始化完成', 'initialize')
    }
    catch (error) {
      moduleErrorHandlers.main.fatal(
        error instanceof Error ? error : String(error),
        'initialize',
      )
    }
    finally {
      // 使用自定义 IPC 系统通知渲染进程应用已初始化
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        try {
          moduleErrorHandlers.main.info('主进程初始化完成，通知渲染进程应用已初始化', 'initialize')
          // 同时发送关闭 loading 的消息
          moduleErrorHandlers.main.info('主进程初始化完成，通知渲染进程关闭 loading', 'initialize')
          AppEvents.emitRemoveLoading(this.mainWindow.webContents.id)
        }
        catch (sendError) {
          moduleErrorHandlers.main.warning(`发送初始化完成消息失败: ${sendError}`, 'initialize')
        }
      }
      else {
        moduleErrorHandlers.main.info('主窗口不存在或已销毁，无法发送初始化完成消息', 'initialize')
      }
    }
  }

  /**
   * 初始化配置
   */
  private async initializeConfig(): Promise<void> {
    this.configManager.initialize()

    const validation = this.configManager.validate()
    if (!validation.isValid) {
      throw new Error(`配置验证失败: ${validation.errors.join(', ')}`)
    }
  }

  /**
   * 初始化 IPC 系统
   */
  private async initializeIPC(): Promise<void> {
    // 初始化主 IPC 系统
    mainIPC.initialize()

    // 注册业务处理器
    // registerBusinessHandlers(mainIPC)

    // 注册自定义 IPC 处理器
    this.ipcManager.registerHandlers(mainIPC)
  }

  /**
   * 创建主窗口
   */
  private async createMainWindow(): Promise<void> {
    const windowOptions = this.configManager.getWindowLoadOptions()
    this.mainWindow = await this.windowManager.createMainWindow(windowOptions)
  }

  /**
   * 初始化 UI 组件
   */
  private async initializeUI(): Promise<void> {
    if (!this.mainWindow) {
      throw new Error('主窗口未创建')
    }

    // 创建右键菜单
    this.menuManager.createContextMenu(this.mainWindow)
    // 创建应用程序菜单栏
    this.menuManager.createApplicationMenu(this.mainWindow)
    // 创建系统托盘
    this.systemTray = await this.trayManager.createTray(this.mainWindow)
    // 创建 Dock 菜单 (macOS)
    // if (this.configManager.getPlatformConfig().IS_MACOS) {
    //   this.menuManager.createDockMenu()
    // }
  }

  /**
   * 设置事件监听器
   */
  private async setupEventListeners(): Promise<void> {
    this.eventManager.setup({
      getMainWindow: () => this.mainWindow,
      setMainWindow: (window) => { this.mainWindow = window },
      cleanup: () => this.cleanup(),
    })
  }

  /**
   * 初始化更新服务
   */
  private async initializeUpdateService(): Promise<void> {
    if (!this.mainWindow) {
      throw new Error('主窗口未创建，无法初始化更新服务')
    }

    // 初始化更新服务
    UpdateService.initialize()

    // 设置主窗口引用
    UpdateService.setMainWindow(this.mainWindow)

    // 在生产环境中启动时检查更新
    if (process.env.NODE_ENV === 'production') {
      // 延迟5秒后检查更新，避免影响应用启动速度
      setTimeout(() => {
        UpdateService.checkForUpdates(true) // 静默检查
      }, 5000)
    }

    moduleErrorHandlers.main.info('更新服务初始化完成', 'initializeUpdateService')
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    try {
      // 清理事件监听器
      this.eventManager.cleanup()

      // 清理 IPC 资源
      mainIPC.dispose()

      // 清理托盘资源
      if (this.systemTray) {
        this.systemTray.destroy()
        this.systemTray = null
      }

      // 清理窗口资源
      this.windowManager.cleanup()

      // 清理更新服务资源
      UpdateService.cleanup()

      moduleErrorHandlers.main.info('应用程序资源清理完成', 'cleanup')
    }
    catch (error) {
      moduleErrorHandlers.main.error(
        error instanceof Error ? error : String(error),
        'cleanup',
      )
    }
  }

  /**
   * 获取主窗口实例
   */
  getMainWindow(): BrowserWindow | null {
    return this.mainWindow
  }

  /**
   * 获取系统托盘实例
   */
  getSystemTray(): Tray | null {
    return this.systemTray
  }
}
