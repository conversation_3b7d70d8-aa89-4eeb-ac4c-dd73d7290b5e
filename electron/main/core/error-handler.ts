/**
 * 统一错误处理模块
 * 提供一致的错误处理、日志记录和用户通知
 */

import { app, dialog } from 'electron'

/**
 * 错误级别枚举
 */
export enum ErrorLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  FATAL = 'fatal',
}

/**
 * 错误上下文信息
 */
export interface ErrorContext {
  module: string
  operation: string
  timestamp: number
  additionalInfo?: Record<string, any>
}

/**
 * 格式化的错误信息
 */
export interface FormattedError {
  level: ErrorLevel
  message: string
  context: ErrorContext
  originalError?: Error
  stack?: string
}

/**
 * 错误处理器配置
 */
export interface ErrorHandlerConfig {
  enableConsoleLogging: boolean
  enableFileLogging: boolean
  enableUserNotification: boolean
  logFilePath?: string
  maxLogFileSize?: number
}

/**
 * 默认错误处理器配置
 */
const DEFAULT_CONFIG: ErrorHandlerConfig = {
  enableConsoleLogging: true,
  enableFileLogging: false,
  enableUserNotification: false,
}

/**
 * 统一错误处理器类
 */
export class ErrorHandler {
  private static instance: ErrorHandler
  private config: ErrorHandlerConfig

  private constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
  }

  /**
   * 获取单例实例
   */
  static getInstance(config?: Partial<ErrorHandlerConfig>): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler(config)
    }
    return ErrorHandler.instance
  }

  /**
   * 处理错误
   */
  handle(
    error: Error | string,
    level: ErrorLevel = ErrorLevel.ERROR,
    context: Partial<ErrorContext> = {},
  ): FormattedError {
    const formattedError = this.formatError(error, level, context)

    // 控制台日志
    if (this.config.enableConsoleLogging) {
      this.logToConsole(formattedError)
    }

    // 文件日志
    if (this.config.enableFileLogging) {
      this.logToFile(formattedError)
    }

    // 用户通知
    if (this.config.enableUserNotification && level === ErrorLevel.FATAL) {
      this.notifyUser(formattedError)
    }

    return formattedError
  }

  /**
   * 格式化错误信息
   */
  private formatError(
    error: Error | string,
    level: ErrorLevel,
    context: Partial<ErrorContext>,
  ): FormattedError {
    const timestamp = Date.now()
    const message = error instanceof Error ? error.message : error
    const stack = error instanceof Error ? error.stack : undefined

    const fullContext: ErrorContext = {
      module: 'unknown',
      operation: 'unknown',
      timestamp,
      ...context,
    }

    return {
      level,
      message,
      context: fullContext,
      originalError: error instanceof Error ? error : undefined,
      stack,
    }
  }

  /**
   * 控制台日志输出
   */
  private logToConsole(error: FormattedError): void {
    const { level, message, context } = error
    const prefix = `[${level.toUpperCase()}] [${context.module}:${context.operation}]`

    switch (level) {
      case ErrorLevel.INFO:
        console.info(prefix, message)
        break
      case ErrorLevel.WARNING:
        console.warn(prefix, message)
        break
      case ErrorLevel.ERROR:
      case ErrorLevel.FATAL:
        console.error(prefix, message)
        if (error.stack) {
          console.error(error.stack)
        }
        break
    }
  }

  /**
   * 文件日志输出
   */
  private logToFile(_error: FormattedError): void {
    // TODO: 实现文件日志功能
    // 可以使用 fs 模块写入日志文件
  }

  /**
   * 用户通知
   */
  private notifyUser(error: FormattedError): void {
    const { message, context } = error

    dialog.showErrorBox(
      '应用程序错误',
      `在 ${context.module} 模块执行 ${context.operation} 操作时发生错误：\n\n${message}\n\n请重启应用程序或联系技术支持。`,
    )
  }

  /**
   * 创建模块特定的错误处理器
   */
  createModuleHandler(moduleName: string) {
    return {
      info: (message: string, operation = 'unknown', additionalInfo?: Record<string, any>) =>
        this.handle(message, ErrorLevel.INFO, { module: moduleName, operation, additionalInfo }),

      warning: (error: Error | string, operation = 'unknown', additionalInfo?: Record<string, any>) =>
        this.handle(error, ErrorLevel.WARNING, { module: moduleName, operation, additionalInfo }),

      error: (error: Error | string, operation = 'unknown', additionalInfo?: Record<string, any>) =>
        this.handle(error, ErrorLevel.ERROR, { module: moduleName, operation, additionalInfo }),

      fatal: (error: Error | string, operation = 'unknown', additionalInfo?: Record<string, any>) => {
        this.handle(error, ErrorLevel.FATAL, { module: moduleName, operation, additionalInfo })
        // 致命错误时退出应用
        app.quit()
        process.exit(1)
      },
    }
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<ErrorHandlerConfig>): void {
    this.config = { ...this.config, ...config }
  }
}

/**
 * 导出默认错误处理器实例
 */
export const errorHandler = ErrorHandler.getInstance({
  enableConsoleLogging: true,
  enableUserNotification: true,
})

/**
 * 导出常用模块的错误处理器
 */
export const moduleErrorHandlers = {
  main: errorHandler.createModuleHandler('main'),
  ipc: errorHandler.createModuleHandler('ipc'),
  window: errorHandler.createModuleHandler('window'),
  config: errorHandler.createModuleHandler('config'),
  tray: errorHandler.createModuleHandler('tray'),
  menu: errorHandler.createModuleHandler('menu'),
  bootstrap: errorHandler.createModuleHandler('bootstrap'),
}
