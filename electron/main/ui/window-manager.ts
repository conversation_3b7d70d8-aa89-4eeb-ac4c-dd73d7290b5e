import path from 'node:path'
import { BrowserWindow, app, screen } from 'electron'
import type { BrowserWindowConstructorOptions } from 'electron'
import type { WindowLoadOptions } from '../config/config-manager'
import { moduleErrorHandlers } from '../core/error-handler'

/**
 * 窗口创建选项
 */
export interface WindowCreateOptions extends WindowLoadOptions {
  width?: number
  height?: number
  minWidth?: number
  minHeight?: number
  resizable?: boolean
  frame?: boolean
  /** 窗口要显示的路由路径 */
  route?: string
  /** 窗口标题 */
  title?: string
  /** 是否为模态窗口 */
  modal?: boolean
  /** 父窗口ID（用于模态窗口） */
  parent?: number
  [key: string]: any
}

/**
 * 窗口管理器
 * 负责窗口的创建、管理和生命周期处理
 */
export class WindowManager {
  private windows: Map<number, BrowserWindow> = new Map()
  private mainWindowId: number | null = null

  /**
   * 创建主窗口
   */
  async createMainWindow(options: WindowLoadOptions): Promise<BrowserWindow> {
    try {
      const { width, height } = screen.getPrimaryDisplay().workAreaSize

      const windowOptions: BrowserWindowConstructorOptions = {
        width: Math.floor(width * 0.6), // 900
        height: Math.floor(height * 0.6), // 600
        minWidth: 400,
        minHeight: 300,
        resizable: true,
        movable: true,
        minimizable: true,
        maximizable: true,
        titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
        icon: path.join(process.env.VITE_PUBLIC || '', 'logo.svg'),
        webPreferences: {
          preload: options.preload,
          nodeIntegration: false,
          contextIsolation: true,
          webSecurity: false, // 在打包环境中禁用webSecurity以允许localStorage访问
          allowRunningInsecureContent: false,
          // 添加额外的安全配置
          experimentalFeatures: false,
          // 允许本地存储访问
          partition: 'persist:main',
        },
      }

      const window = new BrowserWindow(windowOptions)
      this.mainWindowId = window.id
      this.windows.set(window.id, window)

      // 设置窗口事件监听器
      this.setupWindowEvents(window)

      // 设置 macOS 特有配置
      await this.setupMacOSSpecific()

      // 加载窗口内容
      await this.loadWindowContent(window, options)

      // 窗口准备好后显示
      window.once('ready-to-show', () => {
        window.show()

        // 开发环境下打开开发者工具
        if (options.VITE_DEV_SERVER_URL) {
          window.webContents.openDevTools()
        }
      })

      moduleErrorHandlers.window.info(`主窗口创建成功，ID: ${window.id}`, 'createMainWindow')
      return window
    }
    catch (error) {
      moduleErrorHandlers.window.error(
        error instanceof Error ? error : String(error),
        'createMainWindow',
      )
      throw error
    }
  }

  /**
   * 创建子窗口
   */
  async createChildWindow(options: WindowCreateOptions): Promise<BrowserWindow> {
    try {
      // 获取父窗口（如果指定了）
      let parentWindow: BrowserWindow | undefined
      if (options.parent) {
        const parent = BrowserWindow.fromId(options.parent)
        if (parent && !parent.isDestroyed()) {
          parentWindow = parent
        }
      }

      const windowOptions: BrowserWindowConstructorOptions = {
        width: options.width || 800,
        height: options.height || 600,
        minWidth: options.minWidth || 400,
        minHeight: options.minHeight || 300,
        resizable: options.resizable !== false,
        frame: options.frame !== false,
        title: options.title || '新窗口',
        modal: options.modal || false,
        parent: parentWindow,
        webPreferences: {
          preload: options.preload,
          nodeIntegration: false,
          contextIsolation: true,
          webSecurity: false, // 在打包环境中禁用webSecurity以允许localStorage访问
          // 允许本地存储访问
          partition: 'persist:main',
        },
        show: false,
      }

      const window = new BrowserWindow(windowOptions)
      this.windows.set(window.id, window)

      // 设置窗口事件监听器
      this.setupWindowEvents(window)

      // 窗口准备好后显示的事件监听器
      window.once('ready-to-show', () => {
        moduleErrorHandlers.window.info(`窗口准备就绪，开始显示，ID: ${window.id}`, 'createChildWindow')
        window.show()
        window.focus() // 确保窗口获得焦点
        moduleErrorHandlers.window.info(`窗口已显示并获得焦点，ID: ${window.id}`, 'createChildWindow')
      })

      // 加载窗口内容
      moduleErrorHandlers.window.info(`开始加载窗口内容，ID: ${window.id}`, 'createChildWindow')
      await this.loadWindowContent(window, options)
      moduleErrorHandlers.window.info(`窗口内容加载完成，ID: ${window.id}`, 'createChildWindow')

      // 如果 ready-to-show 事件没有触发，强制显示窗口
      setTimeout(() => {
        if (!window.isDestroyed() && !window.isVisible()) {
          moduleErrorHandlers.window.warning(`窗口未自动显示，强制显示，ID: ${window.id}`, 'createChildWindow')
          window.show()
          window.focus()
        }
      }, 2000) // 2秒后检查

      moduleErrorHandlers.window.info(`子窗口创建成功，ID: ${window.id}`, 'createChildWindow')
      return window
    }
    catch (error) {
      moduleErrorHandlers.window.error(
        error instanceof Error ? error : String(error),
        'createChildWindow',
      )
      throw error
    }
  }

  /**
   * 设置窗口事件监听器
   */
  private setupWindowEvents(window: BrowserWindow): void {
    // 窗口关闭事件 - 阻止默认关闭行为，改为隐藏窗口
    window.on('close', (event) => {
      // 阻止默认的关闭行为
      event.preventDefault()

      // 隐藏窗口而不是关闭
      this.hideWindow(window)

      moduleErrorHandlers.window.info(`窗口已隐藏到托盘，ID: ${window.id}`, 'windowClose')
    })

    // 窗口真正关闭事件（当窗口被销毁时）
    window.on('closed', () => {
      this.windows.delete(window.id)
      if (window.id === this.mainWindowId) {
        this.mainWindowId = null
      }
      moduleErrorHandlers.window.info(`窗口已关闭，ID: ${window.id}`, 'windowClosed')
    })

    // 窗口最小化事件
    window.on('minimize', () => {
      moduleErrorHandlers.window.info(`窗口已最小化，ID: ${window.id}`, 'windowMinimized')
    })

    // 窗口最大化事件
    window.on('maximize', () => {
      moduleErrorHandlers.window.info(`窗口已最大化，ID: ${window.id}`, 'windowMaximized')
    })

    // 窗口还原事件
    window.on('restore', () => {
      moduleErrorHandlers.window.info(`窗口已还原，ID: ${window.id}`, 'windowRestored')
    })

    // 窗口失去焦点事件
    window.on('blur', () => {
      moduleErrorHandlers.window.info(`窗口失去焦点，ID: ${window.id}`, 'windowBlur')
    })

    // 窗口获得焦点事件
    window.on('focus', () => {
      moduleErrorHandlers.window.info(`窗口获得焦点，ID: ${window.id}`, 'windowFocus')
    })
  }

  /**
   * 设置 macOS 特有配置
   */
  private async setupMacOSSpecific(): Promise<void> {
    if (process.platform === 'darwin') {
      try {
        const iconPath = path.join(process.env.VITE_PUBLIC || '', 'logo.svg')
        const fs = await import('node:fs')

        if (fs.existsSync(iconPath) && app.dock) {
          app.dock.setIcon(iconPath)
          moduleErrorHandlers.window.info('Dock 图标设置成功', 'setupMacOSSpecific')
        }
      }
      catch (error) {
        moduleErrorHandlers.window.warning(
          error instanceof Error ? error : String(error),
          'setupMacOSSpecific',
        )
      }
    }
  }

  /**
   * 加载窗口内容
   */
  private async loadWindowContent(window: BrowserWindow, options: WindowLoadOptions & { route?: string }): Promise<void> {
    try {
      let url: string

      if (options.VITE_DEV_SERVER_URL) {
        // 开发环境：使用开发服务器
        url = options.VITE_DEV_SERVER_URL
        if (options.route) {
          // 添加路由参数
          url += `#${options.route}`
        }
        moduleErrorHandlers.window.info(`正在加载开发服务器: ${url}`, 'loadWindowContent')
        await window.loadURL(url)
        moduleErrorHandlers.window.info('开发服务器内容加载成功', 'loadWindowContent')
      }
      else {
        // 生产环境：使用静态文件
        moduleErrorHandlers.window.info(`正在加载静态文件: ${options.indexHtml}`, 'loadWindowContent')
        await window.loadFile(options.indexHtml, {
          hash: options.route || '',
        })
        moduleErrorHandlers.window.info('静态文件内容加载成功', 'loadWindowContent')
      }
    }
    catch (error) {
      moduleErrorHandlers.window.error(
        error instanceof Error ? error : String(error),
        'loadWindowContent',
      )
      throw error
    }
  }

  /**
   * 处理所有窗口关闭事件
   * 注意：由于我们现在阻止了窗口的默认关闭行为，这个方法只在真正关闭窗口时才会被调用
   */
  handleAllWindowsClosed(
    setMainWindow: (window: BrowserWindow | null) => void,
    cleanup: () => void,
  ): void {
    // 在 macOS 上，应用通常保持活跃状态，即使所有窗口都关闭了
    if (process.platform !== 'darwin') {
      moduleErrorHandlers.window.info('所有窗口已关闭，退出应用程序', 'handleAllWindowsClosed')
      cleanup()
      app.quit()
    }
    else {
      setMainWindow(null)
      moduleErrorHandlers.window.info('所有窗口已关闭 (macOS)', 'handleAllWindowsClosed')
    }
  }

  /**
   * 处理第二个实例启动事件
   */
  handleSecondInstance(getMainWindow: () => BrowserWindow | null): void {
    const mainWindow = getMainWindow()
    if (mainWindow) {
      // 如果窗口被最小化，恢复它
      if (mainWindow.isMinimized()) {
        mainWindow.restore()
      }
      // 聚焦到窗口
      mainWindow.focus()
      moduleErrorHandlers.window.info('第二个实例启动，聚焦主窗口', 'handleSecondInstance')
    }
  }

  /**
   * 获取窗口实例
   */
  getWindow(windowId: number): BrowserWindow | undefined {
    return this.windows.get(windowId)
  }

  /**
   * 获取主窗口实例
   */
  getMainWindow(): BrowserWindow | null {
    return this.mainWindowId ? this.windows.get(this.mainWindowId) || null : null
  }

  /**
   * 获取所有窗口
   */
  getAllWindows(): BrowserWindow[] {
    return Array.from(this.windows.values())
  }

  /**
   * 隐藏窗口（保留 Dock 图标）
   */
  hideWindow(window: BrowserWindow): void {
    try {
      if (window && !window.isDestroyed()) {
        window.hide()

        // 注意：不隐藏 Dock 图标，让用户可以通过 Dock 重新打开应用
        moduleErrorHandlers.window.info(`窗口已隐藏，ID: ${window.id}`, 'hideWindow')
      }
    }
    catch (error) {
      moduleErrorHandlers.window.error(
        error instanceof Error ? error : String(error),
        'hideWindow',
      )
    }
  }

  /**
   * 显示隐藏的窗口
   */
  showWindow(window: BrowserWindow): void {
    try {
      if (window && !window.isDestroyed()) {
        // macOS 特有：确保 Dock 图标可见（通常已经可见，但确保一下）
        if (process.platform === 'darwin' && app.dock) {
          app.dock.show()
        }

        // 如果窗口被最小化，先恢复
        if (window.isMinimized()) {
          window.restore()
        }

        // 显示并聚焦窗口
        window.show()
        window.focus()

        // macOS 特有：确保应用在前台
        if (process.platform === 'darwin') {
          app.focus({ steal: true })
        }

        moduleErrorHandlers.window.info(`窗口已显示，ID: ${window.id}`, 'showWindow')
      }
    }
    catch (error) {
      moduleErrorHandlers.window.error(
        error instanceof Error ? error : String(error),
        'showWindow',
      )
    }
  }

  /**
   * 关闭窗口（真正关闭）
   */
  closeWindow(windowId: number): boolean {
    const window = this.windows.get(windowId)
    if (window && !window.isDestroyed()) {
      // 移除 close 事件监听器以避免阻止关闭
      window.removeAllListeners('close')
      window.close()
      return true
    }
    return false
  }

  /**
   * 强制退出应用程序（真正关闭所有窗口）
   */
  forceQuit(): void {
    try {
      // 移除所有窗口的 close 事件监听器
      this.windows.forEach((window) => {
        if (!window.isDestroyed()) {
          window.removeAllListeners('close')
        }
      })

      // 退出应用程序
      app.quit()

      moduleErrorHandlers.window.info('强制退出应用程序', 'forceQuit')
    }
    catch (error) {
      moduleErrorHandlers.window.error(
        error instanceof Error ? error : String(error),
        'forceQuit',
      )
    }
  }

  /**
   * 清理所有窗口
   */
  cleanup(): void {
    try {
      // 移除所有窗口的 close 事件监听器，然后关闭窗口
      this.windows.forEach((window, _id) => {
        if (!window.isDestroyed()) {
          window.removeAllListeners('close')
          window.close()
        }
      })
      this.windows.clear()
      this.mainWindowId = null

      moduleErrorHandlers.window.info('窗口管理器清理完成', 'cleanup')
    }
    catch (error) {
      moduleErrorHandlers.window.error(
        error instanceof Error ? error : String(error),
        'cleanup',
      )
    }
  }
}
