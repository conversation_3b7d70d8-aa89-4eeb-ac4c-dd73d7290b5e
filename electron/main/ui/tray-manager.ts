import path from 'node:path'
import fs from 'node:fs'
import type { MenuItemConstructorOptions } from 'electron'
import { BrowserWindow, Menu, Tray, app, nativeImage } from 'electron'
import { moduleErrorHandlers } from '../core/error-handler'

/**
 * 托盘管理器
 * 负责创建和管理系统托盘
 */
export class TrayManager {
  private tray: Tray | null = null

  /**
   * 创建系统托盘
   */
  async createTray(window: BrowserWindow): Promise<Tray> {
    try {
      // 获取托盘图标路径
      const iconPath = this.getTrayIconPath()

      // 创建托盘图标
      let icon: Electron.NativeImage

      if (process.platform === 'darwin') {
        // macOS: 创建支持多分辨率的模板图像
        const basePath = iconPath.replace('.png', '')
        const icon1x = `${basePath}.png`
        const icon2x = `${basePath}@2x.png`

        // 尝试创建多分辨率图标
        try {
          // 优先使用 2x 图标，如果不存在则使用 1x
          if (fs.existsSync(icon2x)) {
            icon = nativeImage.createFromPath(icon2x)
          }
          else if (fs.existsSync(icon1x)) {
            icon = nativeImage.createFromPath(icon1x)

            // 如果有 2x 版本，添加高分辨率表示
            if (fs.existsSync(icon2x)) {
              const icon2xImage = nativeImage.createFromPath(icon2x)
              icon.addRepresentation({
                scaleFactor: 2.0,
                buffer: icon2xImage.toPNG(),
              })
            }
          }
          else {
            // 如果模板图标都不存在，尝试使用通用图标
            const fallbackIcon = path.join(process.env.VITE_PUBLIC || '', 'logo.svg')
            icon = nativeImage.createFromPath(fallbackIcon)
          }
        }
        catch (error) {
          // 如果多分辨率创建失败，使用单一图标
          moduleErrorHandlers.tray.error(`创建多分辨率图标失败: ${error}`, 'createTray')
          icon = nativeImage.createFromPath(iconPath)
        }

        // 设置为模板图像以支持主题自适应
        if (icon && !icon.isEmpty()) {
          icon.setTemplateImage(true)
        }
      }
      else {
        // 其他平台：直接创建图标
        icon = nativeImage.createFromPath(iconPath)
      }

      // 验证图标是否有效
      if (!icon || icon.isEmpty()) {
        throw new Error('托盘图标无效或为空')
      }

      const tray = new Tray(icon)
      this.tray = tray

      // 设置托盘提示文本
      tray.setToolTip(app.getName())

      // 创建托盘菜单
      const contextMenu = this.createTrayMenu()
      tray.setContextMenu(contextMenu)

      // 设置托盘点击事件
      this.setupTrayEvents(window)

      moduleErrorHandlers.tray.info('系统托盘创建成功', 'createTray')
      return tray
    }
    catch (error) {
      moduleErrorHandlers.tray.error(
        error instanceof Error ? error : String(error),
        'createTray',
      )
      throw error
    }
  }

  /**
   * 获取托盘图标路径
   */
  private getTrayIconPath(): string {
    const platform = process.platform
    let iconName = 'logo.svg'

    // 根据平台选择合适的图标
    if (platform === 'win32') {
      iconName = 'logo.ico'
    }
    else if (platform === 'darwin') {
      // macOS 使用模板图像，文件名以 Template 结尾
      iconName = 'logoTemplate.png'
    }

    const publicPath = process.env.VITE_PUBLIC || ''
    const iconPath = path.join(publicPath, iconName)

    return iconPath
  }

  /**
   * 创建托盘菜单（空菜单）
   */
  private createTrayMenu(_window?: BrowserWindow) {
    const template: MenuItemConstructorOptions[] = []

    return Menu.buildFromTemplate(template)
  }

  /**
   * 设置托盘事件
   */
  private setupTrayEvents(window: BrowserWindow): void {
    if (!this.tray)
      return

    // 单击托盘图标事件
    this.tray.on('click', () => {
      this.showWindowIfHidden(window)
    })

    // 双击托盘图标事件
    this.tray.on('double-click', () => {
      this.showWindowIfHidden(window)
    })
  }

  /**
   * 智能显示窗口
   * - 如果窗口隐藏或最小化：显示窗口
   * - 如果窗口可见但失去焦点：聚焦窗口
   * - 如果窗口已经聚焦：不进行任何操作
   */
  private showWindowIfHidden(window: BrowserWindow): void {
    try {
      // 检查窗口是否隐藏或最小化
      if (!window.isVisible() || window.isMinimized()) {
        this.showWindow(window)
      }
      // 检查窗口是否可见但失去焦点
      else if (window.isVisible() && !window.isFocused()) {
        this.focusWindow(window)
      }
      else {
        // 窗口已经聚焦显示，不进行任何操作
        moduleErrorHandlers.tray.info('窗口已聚焦显示，跳过操作', 'showWindowIfHidden')
      }
    }
    catch (error) {
      moduleErrorHandlers.tray.error(
        error instanceof Error ? error : String(error),
        'showWindowIfHidden',
      )
    }
  }

  /**
   * 显示窗口（优化版，减少闪烁）
   */
  private showWindow(window: BrowserWindow): void {
    try {
      // macOS 特有：先显示 Dock 图标，避免闪烁
      if (process.platform === 'darwin' && app.dock) {
        app.dock.show()
      }

      // 处理最小化状态
      if (window.isMinimized()) {
        // 先恢复窗口，但不立即显示
        window.restore()

        // 使用 setImmediate 确保恢复操作完成后再进行后续操作
        setImmediate(() => {
          this.finalizeWindowShow(window)
        })
      }
      else if (!window.isVisible()) {
        // 窗口隐藏状态，直接显示
        this.finalizeWindowShow(window)
      }
      else {
        // 窗口已可见，只需聚焦
        window.focus()
        moduleErrorHandlers.tray.info('窗口已聚焦', 'showWindow')
      }
    }
    catch (error) {
      moduleErrorHandlers.tray.error(
        error instanceof Error ? error : String(error),
        'showWindow',
      )
    }
  }

  /**
   * 完成窗口显示的最终步骤
   */
  private finalizeWindowShow(window: BrowserWindow): void {
    try {
      // 确保窗口可见
      if (!window.isVisible()) {
        window.show()
      }

      // 聚焦窗口
      window.focus()

      // macOS 特有：确保应用在前台
      if (process.platform === 'darwin') {
        app.focus({ steal: true })
      }

      moduleErrorHandlers.tray.info('窗口已显示', 'showWindow')
    }
    catch (error) {
      moduleErrorHandlers.tray.error(
        error instanceof Error ? error : String(error),
        'finalizeWindowShow',
      )
    }
  }

  /**
   * 聚焦窗口（不改变显示状态，优化版）
   */
  private focusWindow(window: BrowserWindow): void {
    try {
      // macOS 特有：先确保 Dock 图标可见
      if (process.platform === 'darwin' && app.dock) {
        app.dock.show()
      }

      // 聚焦窗口
      window.focus()

      // macOS 特有：确保应用在前台，使用更温和的方式
      if (process.platform === 'darwin') {
        app.focus({ steal: false })
      }

      moduleErrorHandlers.tray.info('窗口已聚焦', 'focusWindow')
    }
    catch (error) {
      moduleErrorHandlers.tray.error(
        error instanceof Error ? error : String(error),
        'focusWindow',
      )
    }
  }

  /**
   * 隐藏窗口（保留 Dock 图标）
   */
  private hideWindow(window: BrowserWindow): void {
    try {
      window.hide()

      // 注意：不隐藏 Dock 图标，让用户可以通过 Dock 重新打开应用
      moduleErrorHandlers.tray.info('窗口已隐藏', 'hideWindow')
    }
    catch (error) {
      moduleErrorHandlers.tray.error(
        error instanceof Error ? error : String(error),
        'hideWindow',
      )
    }
  }

  /**
   * 最小化到托盘
   */
  private minimizeToTray(window: BrowserWindow): void {
    try {
      window.hide()

      // 显示气球提示（Windows）
      if (process.platform === 'win32' && this.tray) {
        this.tray.displayBalloon({
          iconType: 'info',
          title: app.getName(),
          content: '应用程序已最小化到系统托盘',
        })
      }

      moduleErrorHandlers.tray.info('窗口已最小化到托盘', 'minimizeToTray')
    }
    catch (error) {
      moduleErrorHandlers.tray.error(
        error instanceof Error ? error : String(error),
        'minimizeToTray',
      )
    }
  }

  /**
   * 切换窗口显示状态
   */
  private toggleWindow(window: BrowserWindow): void {
    try {
      if (window.isVisible() && !window.isMinimized()) {
        this.hideWindow(window)
      }
      else {
        this.showWindow(window)
      }
    }
    catch (error) {
      moduleErrorHandlers.tray.error(
        error instanceof Error ? error : String(error),
        'toggleWindow',
      )
    }
  }

  /**
   * 退出应用程序（强制退出，移除所有close事件监听器）
   */
  private quitApplication(): void {
    try {
      moduleErrorHandlers.tray.info('从托盘退出应用程序', 'quitApplication')

      // 移除所有窗口的 close 事件监听器以避免阻止关闭
      const allWindows = BrowserWindow.getAllWindows()
      allWindows.forEach((window) => {
        if (!window.isDestroyed()) {
          window.removeAllListeners('close')
        }
      })

      // 退出应用程序
      app.quit()
    }
    catch (error) {
      moduleErrorHandlers.tray.error(
        error instanceof Error ? error : String(error),
        'quitApplication',
      )
    }
  }

  /**
   * 更新托盘图标
   */
  updateIcon(iconPath: string): void {
    try {
      if (this.tray) {
        const icon = nativeImage.createFromPath(iconPath)

        // macOS 设置为模板图像
        if (process.platform === 'darwin') {
          icon.setTemplateImage(true)
        }

        this.tray.setImage(icon)
        moduleErrorHandlers.tray.info('托盘图标已更新', 'updateIcon')
      }
    }
    catch (error) {
      moduleErrorHandlers.tray.error(
        error instanceof Error ? error : String(error),
        'updateIcon',
      )
    }
  }

  /**
   * 更新托盘提示文本
   */
  updateToolTip(text: string): void {
    try {
      if (this.tray) {
        this.tray.setToolTip(text)
        moduleErrorHandlers.tray.info(`托盘提示文本已更新: ${text}`, 'updateToolTip')
      }
    }
    catch (error) {
      moduleErrorHandlers.tray.error(
        error instanceof Error ? error : String(error),
        'updateToolTip',
      )
    }
  }

  /**
   * 显示气球提示
   */
  showBalloon(title: string, content: string): void {
    try {
      if (this.tray && process.platform === 'win32') {
        this.tray.displayBalloon({
          iconType: 'info',
          title,
          content,
        })
        moduleErrorHandlers.tray.info(`气球提示已显示: ${title}`, 'showBalloon')
      }
    }
    catch (error) {
      moduleErrorHandlers.tray.error(
        error instanceof Error ? error : String(error),
        'showBalloon',
      )
    }
  }

  /**
   * 获取托盘实例
   */
  getTray(): Tray | null {
    return this.tray
  }

  /**
   * 销毁托盘
   */
  destroy(): void {
    try {
      if (this.tray) {
        this.tray.destroy()
        this.tray = null
        moduleErrorHandlers.tray.info('托盘已销毁', 'destroy')
      }
    }
    catch (error) {
      moduleErrorHandlers.tray.error(
        error instanceof Error ? error : String(error),
        'destroy',
      )
    }
  }

  /**
   * 清理托盘资源
   */
  cleanup(): void {
    this.destroy()
  }
}
