import { app } from 'electron'
import { ApplicationBootstrap } from './core/bootstrap'
import { moduleErrorHandlers } from './core/error-handler'

/**
 * Electron 主进程入口文件
 * 负责应用程序的初始化和启动
 */

// 禁用硬件加速以减少 EGL 错误（macOS 兼容性）
app.disableHardwareAcceleration()

// 添加命令行开关以减少图形相关错误
app.commandLine.appendSwitch('disable-gpu')
app.commandLine.appendSwitch('disable-gpu-sandbox')
app.commandLine.appendSwitch('disable-software-rasterizer')
app.commandLine.appendSwitch('disable-background-timer-throttling')
app.commandLine.appendSwitch('disable-backgrounding-occluded-windows')
app.commandLine.appendSwitch('disable-renderer-backgrounding')
app.commandLine.appendSwitch('disable-features', 'VizDisplayCompositor')

// 全局应用状态
let applicationBootstrap: ApplicationBootstrap | null = null

/**
 * 应用程序启动
 */
async function startApplication(): Promise<void> {
  try {
    applicationBootstrap = new ApplicationBootstrap()
    await applicationBootstrap.initialize()
  }
  catch (error) {
    moduleErrorHandlers.main.fatal(
      error instanceof Error ? error : String(error),
      'applicationStartup',
    )
  }
}

/**
 * 应用程序关闭清理
 */
function cleanupApplication(): void {
  if (applicationBootstrap) {
    applicationBootstrap.cleanup()
    applicationBootstrap = null
  }
}

// 应用准备就绪时启动
app.whenReady().then(startApplication)

// 应用退出前清理资源
app.on('before-quit', cleanupApplication)

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  moduleErrorHandlers.main.fatal(error, 'uncaughtException')
})

process.on('unhandledRejection', (reason) => {
  moduleErrorHandlers.main.fatal(
    reason instanceof Error ? reason : String(reason),
    'unhandledRejection',
  )
})
