import os from 'node:os'
import type { SystemInfo } from '../../types/system-service'
import type { ServiceResult } from './base-service'
import { BaseService } from './base-service'

/**
 * 系统服务
 * 负责处理所有与系统信息相关的功能
 */
export class SystemService extends BaseService {
  protected static serviceName = 'SystemService'

  /**
   * 获取完整系统信息
   * @returns ServiceResult<SystemInfo>
   */
  static getSystemInfo(): ServiceResult<SystemInfo> {
    try {
      const userInfo = os.userInfo()
      const systemInfo: SystemInfo = {
        platform: process.platform,
        arch: process.arch,
        version: os.release(),
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        cpuCount: os.cpus().length,
        hostname: os.hostname(),
        userInfo: {
          username: userInfo.username,
          homedir: userInfo.homedir,
          shell: userInfo.shell || undefined,
        },
      }

      this.logInfo('获取系统信息成功', 'getSystemInfo')
      return this.createSuccessResult(systemInfo)
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'getSystemInfo')
      return this.createErrorResult(
        error instanceof Error ? error.message : '获取系统信息失败',
      )
    }
  }

  /**
   * 获取内存使用情况
   * @returns ServiceResult<{ total: number, free: number, used: number, usagePercent: number }>
   */
  static getMemoryUsage(): ServiceResult<{
    total: number
    free: number
    used: number
    usagePercent: number
  }> {
    try {
      const total = os.totalmem()
      const free = os.freemem()
      const used = total - free
      const usagePercent = Math.round((used / total) * 100)

      const memoryInfo = {
        total,
        free,
        used,
        usagePercent,
      }

      this.logInfo(`内存使用情况: ${usagePercent}% (${Math.round(used / 1024 / 1024 / 1024)}GB/${Math.round(total / 1024 / 1024 / 1024)}GB)`, 'getMemoryUsage')
      return this.createSuccessResult(memoryInfo)
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'getMemoryUsage')
      return this.createErrorResult(
        error instanceof Error ? error.message : '获取内存使用情况失败',
      )
    }
  }

  /**
   * 获取CPU信息
   * @returns ServiceResult<{ count: number, model: string, speed: number }>
   */
  static getCpuInfo(): ServiceResult<{
    count: number
    model: string
    speed: number
  }> {
    try {
      const cpus = os.cpus()
      const cpuInfo = {
        count: cpus.length,
        model: cpus[0]?.model || 'Unknown',
        speed: cpus[0]?.speed || 0,
      }

      this.logInfo(`CPU信息: ${cpuInfo.model} x${cpuInfo.count} @ ${cpuInfo.speed}MHz`, 'getCpuInfo')
      return this.createSuccessResult(cpuInfo)
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'getCpuInfo')
      return this.createErrorResult(
        error instanceof Error ? error.message : '获取CPU信息失败',
      )
    }
  }

  /**
   * 获取网络接口信息
   * @returns ServiceResult<{ interfaces: Record<string, os.NetworkInterfaceInfo[]> }>
   */
  static getNetworkInterfaces(): ServiceResult<{
    interfaces: Record<string, os.NetworkInterfaceInfo[]>
  }> {
    try {
      const interfaces = os.networkInterfaces() || {}
      const safeInterfaces: Record<string, os.NetworkInterfaceInfo[]> = {}
      // 过滤掉undefined值
      Object.entries(interfaces).forEach(([key, value]) => {
        if (value) {
          safeInterfaces[key] = value
        }
      })
      this.logInfo(`获取到 ${Object.keys(interfaces).length} 个网络接口`, 'getNetworkInterfaces')
      return this.createSuccessResult({ interfaces: safeInterfaces })
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'getNetworkInterfaces')
      return this.createErrorResult(
        error instanceof Error ? error.message : '获取网络接口信息失败',
      )
    }
  }
}
