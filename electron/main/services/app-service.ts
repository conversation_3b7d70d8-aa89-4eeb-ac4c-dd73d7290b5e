import { app } from 'electron'
import type { AppInfo } from '../../types/app-service'
import type { ServiceResult } from './base-service'
import { BaseService } from './base-service'

/**
 * 应用服务
 * 负责处理所有与应用程序相关的功能
 */
export class AppService extends BaseService {
  protected static serviceName = 'AppService'

  /**
   * 获取完整应用信息
   * @returns ServiceResult<AppInfo>
   */
  static getAppInfo(): ServiceResult<AppInfo> {
    try {
      const appInfo: AppInfo = {
        name: app.getName(),
        version: app.getVersion(),
        platform: process.platform,
        arch: process.arch,
      }

      this.logInfo('获取应用信息成功', 'getAppInfo')
      return this.createSuccessResult(appInfo)
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'getAppInfo')
      return this.createErrorResult(
        error instanceof Error ? error.message : '获取应用信息失败',
      )
    }
  }

  /**
   * 退出应用
   * @returns ServiceResult<{ success: boolean }>
   */
  static quitApp(): ServiceResult<{ success: boolean }> {
    try {
      this.logInfo('应用程序准备退出', 'quitApp')
      app.quit()
      return this.createSuccessResult({ success: true })
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'quitApp')
      return this.createErrorResult(
        error instanceof Error ? error.message : '退出应用失败',
      )
    }
  }

  /**
   * 重启应用
   * @returns ServiceResult<{ success: boolean }>
   */
  static relaunchApp(): ServiceResult<{ success: boolean }> {
    try {
      this.logInfo('应用程序准备重启', 'relaunchApp')
      app.relaunch()
      app.exit(0)
      return this.createSuccessResult({ success: true })
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'relaunchApp')
      return this.createErrorResult(
        error instanceof Error ? error.message : '重启应用失败',
      )
    }
  }

  /**
   * 获取应用路径
   * @param name 路径名称
   * @returns ServiceResult<{ path: string }>
   */
  static getPath(name: 'home' | 'appData' | 'userData' | 'sessionData' | 'temp' | 'exe' | 'module' | 'desktop' | 'documents' | 'downloads' | 'music' | 'pictures' | 'videos' | 'recent' | 'logs' | 'crashDumps'): ServiceResult<{ path: string }> {
    try {
      const path = app.getPath(name)
      this.logInfo(`获取应用路径 ${name}: ${path}`, 'getPath')
      return this.createSuccessResult({ path })
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'getPath')
      return this.createErrorResult(
        error instanceof Error ? error.message : '获取应用路径失败',
      )
    }
  }
}
