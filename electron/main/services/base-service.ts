import { moduleErrorHandlers } from '../core/error-handler'

/**
 * 服务操作结果基础接口
 */
export interface ServiceResult<T = any> {
  success: boolean
  error?: string
  data?: T
}

/**
 * 基础服务类
 * 提供通用的服务功能和错误处理
 */
export abstract class BaseService {
  protected static serviceName: string = 'BaseService'

  /**
   * 记录信息日志
   */
  protected static logInfo(message: string, method: string): void {
    moduleErrorHandlers.ipc.info(message, `${this.serviceName}.${method}`)
  }

  /**
   * 记录警告日志
   */
  protected static logWarning(message: string, method: string): void {
    moduleErrorHandlers.ipc.warning(message, `${this.serviceName}.${method}`)
  }

  /**
   * 记录错误日志
   */
  protected static logError(error: Error | string, method: string): void {
    moduleErrorHandlers.ipc.error(
      error instanceof Error ? error : String(error),
      `${this.serviceName}.${method}`,
    )
  }

  /**
   * 创建成功结果
   */
  protected static createSuccessResult<T>(data?: T): ServiceResult<T> {
    return {
      success: true,
      data,
    }
  }

  /**
   * 创建失败结果
   */
  protected static createErrorResult<T>(error: string, data?: T): ServiceResult<T> {
    return {
      success: false,
      error,
      data,
    }
  }

  /**
   * 安全执行异步操作
   */
  protected static async safeExecute<T>(
    operation: () => Promise<T>,
    method: string,
    errorMessage: string = '操作失败',
  ): Promise<ServiceResult<T>> {
    try {
      this.logInfo(`开始执行 ${method}`, method)
      const result = await operation()
      this.logInfo(`${method} 执行成功`, method)
      return this.createSuccessResult(result)
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), method)
      return this.createErrorResult(
        error instanceof Error ? error.message : errorMessage,
      )
    }
  }
}
