import os from 'node:os'
import { app, dialog } from 'electron'
import { autoUpdater } from 'electron-updater'
import type { BrowserWindow } from 'electron'
import { mainIPC } from '../../ipc/main-ipc'
import { BaseService } from './base-service'
import type { ServiceResult } from './base-service'

/**
 * 更新状态枚举
 */
export enum UpdateStatus {
  CHECKING = 'checking', // 正在检查更新
  AVAILABLE = 'available', // 有可用更新
  NOT_AVAILABLE = 'not-available', // 没有可用更新
  DOWNLOADING = 'downloading', // 正在下载更新
  DOWNLOADED = 'downloaded', // 更新已下载完成
  ERROR = 'error', // 更新过程中出现错误
  INSTALLING = 'installing', // 正在安装更新
}

/**
 * 更新信息接口
 */
export interface UpdateInfo {
  status: UpdateStatus
  version?: string
  releaseNotes?: string
  progress?: {
    percent: number
    bytesPerSecond: number
    total: number
    transferred: number
  }
  error?: string
}

/**
 * 更新服务类
 * 负责处理应用程序的自动更新功能
 */
export class UpdateService extends BaseService {
  protected static serviceName = 'UpdateService'
  private static mainWindow: BrowserWindow | null = null
  private static isChecking = false
  private static isDownloading = false
  private static isInitialized = false
  private static updateInfo: UpdateInfo = { status: UpdateStatus.NOT_AVAILABLE }

  /**
   * 初始化更新服务
   */
  static initialize(): void {
    if (this.isInitialized) {
      this.logWarning('UpdateService 已经初始化，跳过重复初始化', 'initialize')
      return
    }

    this.setupAutoUpdater()
    this.isInitialized = true
    this.logInfo('UpdateService 初始化完成', 'initialize')
  }

  /**
   * 检查是否已初始化
   */
  static get isReady(): boolean {
    return this.isInitialized && this.mainWindow !== null
  }

  /**
   * 设置主窗口引用
   */
  static setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window

    // 确保服务已初始化
    if (!this.isInitialized) {
      this.initialize()
    }

    // 在生产环境中智能检查更新
    if (process.env.NODE_ENV === 'production') {
      this.scheduleUpdateCheck()
    }

    this.logInfo('主窗口已设置，更新服务准备就绪', 'setMainWindow')
  }

  /**
   * 智能调度更新检查
   */
  private static scheduleUpdateCheck(): void {
    if (!this.mainWindow)
      return

    // 等待窗口完全加载后再检查更新
    if (this.mainWindow.webContents.isLoading()) {
      // 如果还在加载，等待加载完成
      this.mainWindow.webContents.once('did-finish-load', () => {
        this.delayedUpdateCheck()
      })
    }
    else {
      // 已经加载完成，直接延迟检查
      this.delayedUpdateCheck()
    }
  }

  /**
   * 延迟更新检查
   */
  private static delayedUpdateCheck(): void {
    // 根据不同情况使用不同的延迟时间
    const delay = this.getUpdateCheckDelay()

    this.logInfo(`将在 ${delay / 1000} 秒后检查更新`, 'delayedUpdateCheck')

    setTimeout(() => {
      this.checkForUpdates(true) // 静默检查
    }, delay)
  }

  /**
   * 获取更新检查延迟时间
   */
  private static getUpdateCheckDelay(): number {
    // 基础延迟时间
    const baseDelay = 10000 // 10秒

    // 根据系统性能调整
    const totalMemory = os.totalmem()
    const memoryGB = totalMemory / (1024 * 1024 * 1024)

    if (memoryGB < 4) {
      // 低内存设备，延长等待时间
      return baseDelay + 10000 // 20秒
    }
    else if (memoryGB < 8) {
      // 中等内存设备
      return baseDelay + 5000 // 15秒
    }
    else {
      // 高性能设备
      return baseDelay // 10秒
    }
  }

  /**
   * 配置 autoUpdater
   */
  private static setupAutoUpdater(): void {
    // 配置更新服务器
    autoUpdater.setFeedURL({
      provider: 'generic',
      url: 'https://example.com/auto-updates', // 替换为您的更新服务器地址
      channel: 'latest',
    })

    // 根据平台配置更新策略
    if (process.platform === 'darwin') {
      // macOS 只使用全量更新
      autoUpdater.allowDowngrade = false
      autoUpdater.allowPrerelease = false
    }
    else {
      // Windows/Linux 支持增量更新
      autoUpdater.allowDowngrade = false
      autoUpdater.allowPrerelease = false
    }

    // 配置日志
    autoUpdater.logger = {
      info: message => this.logInfo(message, 'autoUpdater'),
      warn: message => this.logWarning(message, 'autoUpdater'),
      error: message => this.logError(message, 'autoUpdater'),
      debug: message => this.logInfo(`[DEBUG] ${message}`, 'autoUpdater'),
    }

    this.setupEventListeners()
  }

  /**
   * 设置事件监听器
   */
  private static setupEventListeners(): void {
    // 检查更新开始
    autoUpdater.on('checking-for-update', () => {
      this.isChecking = true
      this.updateInfo = { status: UpdateStatus.CHECKING }
      this.notifyRenderer()
      this.logInfo('开始检查更新', 'setupEventListeners')
    })

    // 发现可用更新
    autoUpdater.on('update-available', (info) => {
      this.isChecking = false
      this.updateInfo = {
        status: UpdateStatus.AVAILABLE,
        version: info.version,
        releaseNotes: info.releaseNotes as string,
      }
      this.notifyRenderer()
      this.logInfo(`发现新版本: ${info.version}`, 'setupEventListeners')
    })

    // 没有可用更新
    autoUpdater.on('update-not-available', (info) => {
      this.isChecking = false
      this.updateInfo = {
        status: UpdateStatus.NOT_AVAILABLE,
        version: info.version,
      }
      this.notifyRenderer()
      this.logInfo('当前已是最新版本', 'setupEventListeners')
    })

    // 下载进度
    autoUpdater.on('download-progress', (progressObj) => {
      this.updateInfo = {
        status: UpdateStatus.DOWNLOADING,
        progress: {
          percent: progressObj.percent,
          bytesPerSecond: progressObj.bytesPerSecond,
          total: progressObj.total,
          transferred: progressObj.transferred,
        },
      }
      this.notifyRenderer()
    })

    // 下载完成
    autoUpdater.on('update-downloaded', (info) => {
      this.isDownloading = false
      this.updateInfo = {
        status: UpdateStatus.DOWNLOADED,
        version: info.version,
        releaseNotes: info.releaseNotes as string,
      }
      this.notifyRenderer()
      this.logInfo('更新下载完成', 'setupEventListeners')

      // 询问用户是否立即安装
      this.promptInstallUpdate()
    })

    // 更新错误
    autoUpdater.on('error', (error) => {
      this.isChecking = false
      this.isDownloading = false
      this.updateInfo = {
        status: UpdateStatus.ERROR,
        error: error.message,
      }
      this.notifyRenderer()
      this.logError(`更新错误: ${error.message}`, 'setupEventListeners')
    })
  }

  /**
   * 检查更新
   */
  static async checkForUpdates(_silent = false): Promise<ServiceResult<void>> {
    // 确保服务已初始化
    if (!this.isInitialized) {
      this.initialize()
    }

    return this.safeExecute(
      async () => {
        if (this.isChecking) {
          this.logWarning('正在检查更新中，请稍候', 'checkForUpdates')
          return
        }

        await autoUpdater.checkForUpdatesAndNotify()
      },
      'checkForUpdates',
      '检查更新失败',
    )
  }

  /**
   * 下载更新
   */
  static async downloadUpdate(): Promise<ServiceResult<void>> {
    return this.safeExecute(
      async () => {
        if (this.isDownloading) {
          this.logWarning('正在下载更新中', 'downloadUpdate')
          return
        }

        if (this.updateInfo.status !== UpdateStatus.AVAILABLE) {
          this.logWarning('没有可用的更新', 'downloadUpdate')
          return
        }

        this.isDownloading = true
        await autoUpdater.downloadUpdate()
      },
      'downloadUpdate',
      '下载更新失败',
    )
  }

  /**
   * 安装更新并重启应用
   */
  static installUpdate(): ServiceResult<void> {
    try {
      if (this.updateInfo.status !== UpdateStatus.DOWNLOADED) {
        this.logWarning('更新尚未下载完成', 'installUpdate')
        return this.createErrorResult('更新尚未下载完成')
      }

      this.updateInfo.status = UpdateStatus.INSTALLING
      this.notifyRenderer()

      this.logInfo('开始安装更新并重启应用', 'installUpdate')
      autoUpdater.quitAndInstall()

      return this.createSuccessResult()
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'installUpdate')
      return this.createErrorResult(error instanceof Error ? error.message : '安装更新失败')
    }
  }

  /**
   * 提示用户安装更新
   */
  private static async promptInstallUpdate(): Promise<void> {
    if (!this.mainWindow)
      return

    const result = await dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: '更新下载完成',
      message: '新版本已下载完成，是否立即重启应用进行安装？',
      detail: '您可以选择稍后手动重启应用来完成更新。',
      buttons: ['立即重启', '稍后重启'],
      defaultId: 0,
      cancelId: 1,
    })

    if (result.response === 0) {
      this.installUpdate()
    }
  }

  /**
   * 通知渲染进程更新状态
   */
  private static notifyRenderer(): void {
    try {
      // 使用 mainIPC 发送事件到所有渲染进程
      mainIPC.emit('update-status', this.updateInfo)
      this.logInfo('更新状态已通知到渲染进程', 'notifyRenderer')
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'notifyRenderer')
    }
  }

  /**
   * 获取当前更新信息
   */
  static getUpdateInfo(): UpdateInfo {
    return { ...this.updateInfo }
  }

  /**
   * 获取当前应用版本
   */
  static getCurrentVersion(): string {
    return app.getVersion()
  }

  /**
   * 设置更新服务器地址
   */
  static setUpdateServerUrl(url: string): ServiceResult<void> {
    try {
      autoUpdater.setFeedURL({
        provider: 'generic',
        url,
        channel: 'latest',
      })
      this.logInfo(`更新服务器地址设置为: ${url}`, 'setUpdateServerUrl')
      return this.createSuccessResult()
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'setUpdateServerUrl')
      return this.createErrorResult(error instanceof Error ? error.message : '设置更新服务器失败')
    }
  }

  /**
   * 清理资源
   */
  static cleanup(): void {
    autoUpdater.removeAllListeners()
    this.mainWindow = null
    this.logInfo('UpdateService 资源清理完成', 'cleanup')
  }
}
