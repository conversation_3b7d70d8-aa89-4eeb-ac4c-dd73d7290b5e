import { app, dialog } from 'electron'
import { autoUpdater } from 'electron-updater'
import type { BrowserWindow } from 'electron'
import { moduleErrorHandlers } from '../core/error-handler'

/**
 * 更新状态枚举
 */
export enum UpdateStatus {
  CHECKING = 'checking',
  AVAILABLE = 'available',
  NOT_AVAILABLE = 'not-available',
  DOWNLOADING = 'downloading',
  DOWNLOADED = 'downloaded',
  ERROR = 'error',
  INSTALLING = 'installing',
}

/**
 * 更新信息接口
 */
export interface UpdateInfo {
  status: UpdateStatus
  version?: string
  releaseNotes?: string
  progress?: {
    percent: number
    bytesPerSecond: number
    total: number
    transferred: number
  }
  error?: string
}

/**
 * 更新服务类
 * 负责处理应用程序的自动更新功能
 */
export class UpdateService {
  private mainWindow: BrowserWindow | null = null
  private isChecking = false
  private isDownloading = false
  private updateInfo: UpdateInfo = { status: UpdateStatus.NOT_AVAILABLE }

  constructor() {
    this.setupAutoUpdater()
  }

  /**
   * 设置主窗口引用
   */
  setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window
  }

  /**
   * 配置 autoUpdater
   */
  private setupAutoUpdater(): void {
    // 配置更新服务器
    autoUpdater.setFeedURL({
      provider: 'generic',
      url: 'https://example.com/auto-updates', // 替换为您的更新服务器地址
      channel: 'latest',
    })

    // 根据平台配置更新策略
    if (process.platform === 'darwin') {
      // macOS 只使用全量更新
      autoUpdater.allowDowngrade = false
      autoUpdater.allowPrerelease = false
    }
    else {
      // Windows/Linux 支持增量更新
      autoUpdater.allowDowngrade = false
      autoUpdater.allowPrerelease = false
    }

    // 配置日志
    autoUpdater.logger = {
      info: message => moduleErrorHandlers.main.info(message, 'UpdateService'),
      warn: message => moduleErrorHandlers.main.warning(message, 'UpdateService'),
      error: message => moduleErrorHandlers.main.error(message, 'UpdateService'),
      debug: message => moduleErrorHandlers.main.debug(message, 'UpdateService'),
    }

    this.setupEventListeners()
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 检查更新开始
    autoUpdater.on('checking-for-update', () => {
      this.isChecking = true
      this.updateInfo = { status: UpdateStatus.CHECKING }
      this.notifyRenderer()
      moduleErrorHandlers.main.info('开始检查更新', 'UpdateService')
    })

    // 发现可用更新
    autoUpdater.on('update-available', (info) => {
      this.isChecking = false
      this.updateInfo = {
        status: UpdateStatus.AVAILABLE,
        version: info.version,
        releaseNotes: info.releaseNotes as string,
      }
      this.notifyRenderer()
      moduleErrorHandlers.main.info(`发现新版本: ${info.version}`, 'UpdateService')
    })

    // 没有可用更新
    autoUpdater.on('update-not-available', (info) => {
      this.isChecking = false
      this.updateInfo = {
        status: UpdateStatus.NOT_AVAILABLE,
        version: info.version,
      }
      this.notifyRenderer()
      moduleErrorHandlers.main.info('当前已是最新版本', 'UpdateService')
    })

    // 下载进度
    autoUpdater.on('download-progress', (progressObj) => {
      this.updateInfo = {
        status: UpdateStatus.DOWNLOADING,
        progress: {
          percent: progressObj.percent,
          bytesPerSecond: progressObj.bytesPerSecond,
          total: progressObj.total,
          transferred: progressObj.transferred,
        },
      }
      this.notifyRenderer()
    })

    // 下载完成
    autoUpdater.on('update-downloaded', (info) => {
      this.isDownloading = false
      this.updateInfo = {
        status: UpdateStatus.DOWNLOADED,
        version: info.version,
        releaseNotes: info.releaseNotes as string,
      }
      this.notifyRenderer()
      moduleErrorHandlers.main.info('更新下载完成', 'UpdateService')

      // 询问用户是否立即安装
      this.promptInstallUpdate()
    })

    // 更新错误
    autoUpdater.on('error', (error) => {
      this.isChecking = false
      this.isDownloading = false
      this.updateInfo = {
        status: UpdateStatus.ERROR,
        error: error.message,
      }
      this.notifyRenderer()
      moduleErrorHandlers.main.error(`更新错误: ${error.message}`, 'UpdateService')
    })
  }

  /**
   * 检查更新
   */
  async checkForUpdates(silent = false): Promise<void> {
    if (this.isChecking) {
      moduleErrorHandlers.main.warning('正在检查更新中，请稍候', 'UpdateService')
      return
    }

    try {
      await autoUpdater.checkForUpdatesAndNotify()
    }
    catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      moduleErrorHandlers.main.error(`检查更新失败: ${errorMessage}`, 'UpdateService')

      if (!silent && this.mainWindow) {
        dialog.showErrorBox('更新检查失败', `无法检查更新: ${errorMessage}`)
      }
    }
  }

  /**
   * 下载更新
   */
  async downloadUpdate(): Promise<void> {
    if (this.isDownloading) {
      moduleErrorHandlers.main.warning('正在下载更新中', 'UpdateService')
      return
    }

    if (this.updateInfo.status !== UpdateStatus.AVAILABLE) {
      moduleErrorHandlers.main.warning('没有可用的更新', 'UpdateService')
      return
    }

    try {
      this.isDownloading = true
      await autoUpdater.downloadUpdate()
    }
    catch (error) {
      this.isDownloading = false
      const errorMessage = error instanceof Error ? error.message : String(error)
      moduleErrorHandlers.main.error(`下载更新失败: ${errorMessage}`, 'UpdateService')

      if (this.mainWindow) {
        dialog.showErrorBox('下载失败', `更新下载失败: ${errorMessage}`)
      }
    }
  }

  /**
   * 安装更新并重启应用
   */
  installUpdate(): void {
    if (this.updateInfo.status !== UpdateStatus.DOWNLOADED) {
      moduleErrorHandlers.main.warning('更新尚未下载完成', 'UpdateService')
      return
    }

    this.updateInfo.status = UpdateStatus.INSTALLING
    this.notifyRenderer()

    moduleErrorHandlers.main.info('开始安装更新并重启应用', 'UpdateService')
    autoUpdater.quitAndInstall()
  }

  /**
   * 提示用户安装更新
   */
  private async promptInstallUpdate(): Promise<void> {
    if (!this.mainWindow)
      return

    const result = await dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: '更新下载完成',
      message: '新版本已下载完成，是否立即重启应用进行安装？',
      detail: '您可以选择稍后手动重启应用来完成更新。',
      buttons: ['立即重启', '稍后重启'],
      defaultId: 0,
      cancelId: 1,
    })

    if (result.response === 0) {
      this.installUpdate()
    }
  }

  /**
   * 通知渲染进程更新状态
   */
  private notifyRenderer(): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('update-status', this.updateInfo)
    }
  }

  /**
   * 获取当前更新信息
   */
  getUpdateInfo(): UpdateInfo {
    return { ...this.updateInfo }
  }

  /**
   * 获取当前应用版本
   */
  getCurrentVersion(): string {
    return app.getVersion()
  }

  /**
   * 设置更新服务器地址
   */
  setUpdateServerUrl(url: string): void {
    autoUpdater.setFeedURL({
      provider: 'generic',
      url,
      channel: 'latest',
    })
    moduleErrorHandlers.main.info(`更新服务器地址设置为: ${url}`, 'UpdateService')
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    autoUpdater.removeAllListeners()
    this.mainWindow = null
    moduleErrorHandlers.main.info('UpdateService 资源清理完成', 'UpdateService')
  }
}

// 导出单例实例
export const updateService = new UpdateService()
