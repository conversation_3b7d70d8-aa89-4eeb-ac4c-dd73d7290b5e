import { app, dialog } from 'electron'
import { autoUpdater } from 'electron-updater'
import type { BrowserWindow } from 'electron'
import { BaseService } from './base-service'
import type { ServiceResult } from './base-service'

/**
 * 更新状态枚举
 */
export enum UpdateStatus {
  CHECKING = 'checking',
  AVAILABLE = 'available',
  NOT_AVAILABLE = 'not-available',
  DOWNLOADING = 'downloading',
  DOWNLOADED = 'downloaded',
  ERROR = 'error',
  INSTALLING = 'installing',
}

/**
 * 更新信息接口
 */
export interface UpdateInfo {
  status: UpdateStatus
  version?: string
  releaseNotes?: string
  progress?: {
    percent: number
    bytesPerSecond: number
    total: number
    transferred: number
  }
  error?: string
}

/**
 * 更新服务类
 * 负责处理应用程序的自动更新功能
 */
export class UpdateService extends BaseService {
  protected static serviceName = 'UpdateService'
  private static mainWindow: BrowserWindow | null = null
  private static isChecking = false
  private static isDownloading = false
  private static updateInfo: UpdateInfo = { status: UpdateStatus.NOT_AVAILABLE }

  /**
   * 初始化更新服务
   */
  static initialize(): void {
    this.setupAutoUpdater()
  }

  /**
   * 设置主窗口引用
   */
  static setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window
  }

  /**
   * 配置 autoUpdater
   */
  private static setupAutoUpdater(): void {
    // 配置更新服务器
    autoUpdater.setFeedURL({
      provider: 'generic',
      url: 'https://example.com/auto-updates', // 替换为您的更新服务器地址
      channel: 'latest',
    })

    // 根据平台配置更新策略
    if (process.platform === 'darwin') {
      // macOS 只使用全量更新
      autoUpdater.allowDowngrade = false
      autoUpdater.allowPrerelease = false
    }
    else {
      // Windows/Linux 支持增量更新
      autoUpdater.allowDowngrade = false
      autoUpdater.allowPrerelease = false
    }

    // 配置日志
    autoUpdater.logger = {
      info: message => this.logInfo(message, 'autoUpdater'),
      warn: message => this.logWarning(message, 'autoUpdater'),
      error: message => this.logError(message, 'autoUpdater'),
      debug: message => this.logInfo(`[DEBUG] ${message}`, 'autoUpdater'),
    }

    this.setupEventListeners()
  }

  /**
   * 设置事件监听器
   */
  private static setupEventListeners(): void {
    // 检查更新开始
    autoUpdater.on('checking-for-update', () => {
      this.isChecking = true
      this.updateInfo = { status: UpdateStatus.CHECKING }
      this.notifyRenderer()
      this.logInfo('开始检查更新', 'setupEventListeners')
    })

    // 发现可用更新
    autoUpdater.on('update-available', (info) => {
      this.isChecking = false
      this.updateInfo = {
        status: UpdateStatus.AVAILABLE,
        version: info.version,
        releaseNotes: info.releaseNotes as string,
      }
      this.notifyRenderer()
      this.logInfo(`发现新版本: ${info.version}`, 'setupEventListeners')
    })

    // 没有可用更新
    autoUpdater.on('update-not-available', (info) => {
      this.isChecking = false
      this.updateInfo = {
        status: UpdateStatus.NOT_AVAILABLE,
        version: info.version,
      }
      this.notifyRenderer()
      this.logInfo('当前已是最新版本', 'setupEventListeners')
    })

    // 下载进度
    autoUpdater.on('download-progress', (progressObj) => {
      this.updateInfo = {
        status: UpdateStatus.DOWNLOADING,
        progress: {
          percent: progressObj.percent,
          bytesPerSecond: progressObj.bytesPerSecond,
          total: progressObj.total,
          transferred: progressObj.transferred,
        },
      }
      this.notifyRenderer()
    })

    // 下载完成
    autoUpdater.on('update-downloaded', (info) => {
      this.isDownloading = false
      this.updateInfo = {
        status: UpdateStatus.DOWNLOADED,
        version: info.version,
        releaseNotes: info.releaseNotes as string,
      }
      this.notifyRenderer()
      this.logInfo('更新下载完成', 'setupEventListeners')

      // 询问用户是否立即安装
      this.promptInstallUpdate()
    })

    // 更新错误
    autoUpdater.on('error', (error) => {
      this.isChecking = false
      this.isDownloading = false
      this.updateInfo = {
        status: UpdateStatus.ERROR,
        error: error.message,
      }
      this.notifyRenderer()
      this.logError(`更新错误: ${error.message}`, 'setupEventListeners')
    })
  }

  /**
   * 检查更新
   */
  static async checkForUpdates(_silent = false): Promise<ServiceResult<void>> {
    return this.safeExecute(
      async () => {
        if (this.isChecking) {
          this.logWarning('正在检查更新中，请稍候', 'checkForUpdates')
          return
        }

        await autoUpdater.checkForUpdatesAndNotify()
      },
      'checkForUpdates',
      '检查更新失败',
    )
  }

  /**
   * 下载更新
   */
  static async downloadUpdate(): Promise<ServiceResult<void>> {
    return this.safeExecute(
      async () => {
        if (this.isDownloading) {
          this.logWarning('正在下载更新中', 'downloadUpdate')
          return
        }

        if (this.updateInfo.status !== UpdateStatus.AVAILABLE) {
          this.logWarning('没有可用的更新', 'downloadUpdate')
          return
        }

        this.isDownloading = true
        await autoUpdater.downloadUpdate()
      },
      'downloadUpdate',
      '下载更新失败',
    )
  }

  /**
   * 安装更新并重启应用
   */
  static installUpdate(): ServiceResult<void> {
    try {
      if (this.updateInfo.status !== UpdateStatus.DOWNLOADED) {
        this.logWarning('更新尚未下载完成', 'installUpdate')
        return this.createErrorResult('更新尚未下载完成')
      }

      this.updateInfo.status = UpdateStatus.INSTALLING
      this.notifyRenderer()

      this.logInfo('开始安装更新并重启应用', 'installUpdate')
      autoUpdater.quitAndInstall()

      return this.createSuccessResult()
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'installUpdate')
      return this.createErrorResult(error instanceof Error ? error.message : '安装更新失败')
    }
  }

  /**
   * 提示用户安装更新
   */
  private static async promptInstallUpdate(): Promise<void> {
    if (!this.mainWindow)
      return

    const result = await dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: '更新下载完成',
      message: '新版本已下载完成，是否立即重启应用进行安装？',
      detail: '您可以选择稍后手动重启应用来完成更新。',
      buttons: ['立即重启', '稍后重启'],
      defaultId: 0,
      cancelId: 1,
    })

    if (result.response === 0) {
      this.installUpdate()
    }
  }

  /**
   * 通知渲染进程更新状态
   */
  private static notifyRenderer(): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('update-status', this.updateInfo)
    }
  }

  /**
   * 获取当前更新信息
   */
  static getUpdateInfo(): UpdateInfo {
    return { ...this.updateInfo }
  }

  /**
   * 获取当前应用版本
   */
  static getCurrentVersion(): string {
    return app.getVersion()
  }

  /**
   * 设置更新服务器地址
   */
  static setUpdateServerUrl(url: string): ServiceResult<void> {
    try {
      autoUpdater.setFeedURL({
        provider: 'generic',
        url,
        channel: 'latest',
      })
      this.logInfo(`更新服务器地址设置为: ${url}`, 'setUpdateServerUrl')
      return this.createSuccessResult()
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'setUpdateServerUrl')
      return this.createErrorResult(error instanceof Error ? error.message : '设置更新服务器失败')
    }
  }

  /**
   * 清理资源
   */
  static cleanup(): void {
    autoUpdater.removeAllListeners()
    this.mainWindow = null
    this.logInfo('UpdateService 资源清理完成', 'cleanup')
  }
}
