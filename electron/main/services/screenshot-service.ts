import { BrowserWindow, app, desktopCapturer, dialog, shell, systemPreferences, webContents } from 'electron'
import type { ScreenshotResult, ScreenshotSource } from '../../types/screenshot-service'
import { BaseService } from './base-service'

/**
 * 截图服务
 * 负责处理所有与屏幕截图相关的功能
 */
export class ScreenshotService extends BaseService {
  protected static serviceName = 'ScreenshotService'

  /**
   * 显示权限提示对话框
   */
  private static async showPermissionDialog(): Promise<void> {
    const result = await dialog.showMessageBox({
      type: 'warning',
      title: '需要屏幕录制权限',
      message: '此应用需要屏幕录制权限才能获取截图源',
      detail: `请在系统偏好设置 > 安全性与隐私 > 隐私 > 屏幕录制中，勾选 "${app.getName()}" 以授权屏幕录制权限。\n\n授权后请重新尝试截图功能。`,
      buttons: ['打开系统偏好设置', '取消'],
      defaultId: 0,
      cancelId: 1,
    })

    if (result.response === 0) {
      // 打开系统偏好设置的隐私页面
      await shell.openExternal('x-apple.systempreferences:com.apple.preference.security?Privacy_ScreenCapture')
    }
  }

  /**
   * 检查屏幕录制权限
   * @returns Promise<boolean>
   */
  private static async checkScreenCapturePermission(): Promise<boolean> {
    try {
      if (process.platform === 'darwin') {
        // 开发环境跳过权限检查（仅用于开发测试）
        // macOS 需要检查屏幕录制权限
        const status = systemPreferences.getMediaAccessStatus('screen')
        this.logInfo(`屏幕录制权限状态: ${status}`, 'checkScreenCapturePermission')

        if (status === 'not-determined' || status === 'denied') {
          // 对于屏幕录制权限，我们无法通过代码直接请求
          // 需要用户手动在系统偏好设置中授权
          this.logInfo('需要用户手动授权屏幕录制权限', 'checkScreenCapturePermission')
          await this.showPermissionDialog()
          return false
        }

        return status === 'granted'
      }

      // 其他平台默认有权限
      return true
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'checkScreenCapturePermission')
      return false
    }
  }

  /**
   * 获取所有可用的截图源
   * @param _windowId 窗口ID（当前未使用，保留用于未来扩展）
   * @returns Promise<ScreenshotResult>
   */
  static async getSources(_windowId?: number): Promise<ScreenshotResult> {
    return this.safeExecute(
      async () => {
        this.logInfo('开始获取截图源...', 'getSources')

        // 生产环境才检查权限
        const hasPermission = await this.checkScreenCapturePermission()
        if (!hasPermission) {
          throw new Error('没有屏幕录制权限。请在系统偏好设置 > 安全性与隐私 > 隐私 > 屏幕录制中授权此应用。')
        }

        try {
          // 获取系统窗口和屏幕源
          this.logInfo('正在获取系统截图源...', 'getSources')
          const systemSources = await desktopCapturer.getSources({
            types: ['window', 'screen'],
          })

          this.logInfo(`获取到 ${systemSources.length} 个系统截图源`, 'getSources')

          // 获取当前应用的所有窗口（优化版本）
          const appWindows = await Promise.all(
            webContents
              .getAllWebContents()
              .filter((item) => {
                const win = BrowserWindow.fromWebContents(item)
                return win && win.isVisible()
              })
              .map(async (item) => {
                const win = BrowserWindow.fromWebContents(item)
                const thumbnail = await win?.capturePage()
                return {
                  name: win?.getTitle() + (item.devToolsWebContents === null ? '' : '-dev'), // 给dev窗口加上后缀
                  id: win?.getMediaSourceId() || `window-${item.id}`, // 确保id不为undefined
                  thumbnail,
                  display_id: '',
                  appIcon: null,
                  type: 'window' as const,
                }
              }),
          )

          const allSources: ScreenshotSource[] = [
            ...systemSources.map(source => ({
              name: source.name,
              id: source.id,
              thumbnail: source.thumbnail,
              display_id: source.display_id,
              appIcon: null,
              type: source.id.startsWith('screen:') ? 'screen' as const : 'window' as const,
            })),
            ...appWindows,
          ]

          this.logInfo(`总共获取到 ${allSources.length} 个截图源`, 'getSources')

          return { sources: allSources }
        }
        catch (error) {
          this.logError(error instanceof Error ? error : String(error), 'getSources')
          throw error
        }
      },
      'getSources',
      '获取截图源失败',
    )
  }
}
