import { WindowManager } from '../ui/window-manager'
import { ConfigManager } from '../config/config-manager'
import type { WindowCreateOptions } from '../../types/window-service'
import type { ServiceResult } from './base-service'
import { BaseService } from './base-service'

/**
 * 窗口服务
 * 负责处理所有与窗口操作相关的功能
 */
export class WindowService extends BaseService {
  protected static serviceName = 'WindowService'
  private static windowManager = new WindowManager()
  private static configManager = new ConfigManager()

  /**
   * 创建新窗口
   * @param options 窗口创建选项
   * @returns Promise<ServiceResult<{ windowId: number }>>
   */
  static async createWindow(options: WindowCreateOptions): Promise<ServiceResult<{ windowId: number }>> {
    return this.safeExecute(
      async () => {
        // 获取窗口加载配置
        const windowLoadOptions = this.configManager.getWindowLoadOptions()

        const window = await this.windowManager.createChildWindow({
          preload: windowLoadOptions.preload,
          indexHtml: windowLoadOptions.indexHtml,
          VITE_DEV_SERVER_URL: windowLoadOptions.VITE_DEV_SERVER_URL,
          ...options,
        })

        this.logInfo(`成功创建窗口，ID: ${window.id}`, 'createWindow')
        return { windowId: window.id }
      },
      'createWindow',
      '创建窗口失败',
    )
  }
}
