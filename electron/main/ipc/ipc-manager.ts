// 这个文件负责注册和管理所有从渲染进程发送到主进程的 IPC 请求处理器。

import type { MainIPC } from '../../ipc/main-ipc'
import {
  AppService,
  ScreenshotService,
  SystemService,
  WindowService,
} from '../services'
import { moduleErrorHandlers } from '../core/error-handler'
import { UpdateHandler } from './update-handler'

/**
 * IPC 管理器
 * 负责注册和管理所有自定义 IPC 处理器
 */
export class IPCManager {
  private isRegistered = false

  /**
   * 注册所有 IPC 处理器
   */
  registerHandlers(ipc: MainIPC): void {
    if (this.isRegistered) {
      moduleErrorHandlers.ipc.warning('IPC 处理器已注册，跳过重复注册', 'registerHandlers')
      return
    }

    try {
      // 窗口管理相关处理器
      this.registerWindowHandlers(ipc)

      // 应用状态相关处理器
      this.registerAppHandlers(ipc)

      // 系统信息相关处理器
      this.registerSystemHandlers(ipc)

      // 更新相关处理器
      this.registerUpdateHandlers(ipc)

      this.isRegistered = true
      moduleErrorHandlers.ipc.info('所有 IPC 处理器注册完成', 'registerHandlers')
    }
    catch (error) {
      moduleErrorHandlers.ipc.error(
        error instanceof Error ? error : String(error),
        'registerHandlers',
      )
    }
  }

  /**
   * 注册窗口管理相关处理器
   */
  private registerWindowHandlers(ipc: MainIPC): void {
    // 创建窗口
    ipc.handle('window:create', async (options: any) => {
      return await WindowService.createWindow(options)
    })

    // 截屏
    ipc.handle('window:screenshot', async (windowId: number) => {
      moduleErrorHandlers.ipc.info(`开始获取窗口截图，windowId: ${windowId}`, 'registerWindowHandlers')
      try {
        const result = await ScreenshotService.getSources(windowId)
        moduleErrorHandlers.ipc.info('截图获取成功', 'registerWindowHandlers')
        return result
      }
      catch (error) {
        moduleErrorHandlers.ipc.error(
          error instanceof Error ? error : String(error),
          'window:screenshot',
        )
        throw error
      }
    })

    moduleErrorHandlers.ipc.info('窗口管理处理器注册完成', 'registerWindowHandlers')
  }

  /**
   * 注册应用状态相关处理器
   */
  private registerAppHandlers(ipc: MainIPC): void {
    // 获取应用信息
    ipc.handle('app:getInfo', () => {
      return AppService.getAppInfo()
    })

    // 退出应用
    ipc.handle('app:quit', () => {
      return AppService.quitApp()
    })

    // 重启应用
    ipc.handle('app:relaunch', () => {
      return AppService.relaunchApp()
    })

    // 获取应用路径
    ipc.handle('app:getPath', (name: string) => {
      return AppService.getPath(name as any)
    })

    moduleErrorHandlers.ipc.info('应用状态处理器注册完成', 'registerAppHandlers')
  }

  /**
   * 注册系统信息相关处理器
   */
  private registerSystemHandlers(ipc: MainIPC): void {
    // 获取系统信息
    ipc.handle('system:getSystemInfo', () => {
      return SystemService.getSystemInfo()
    })

    // 获取内存使用情况
    ipc.handle('system:getMemoryUsage', () => {
      return SystemService.getMemoryUsage()
    })

    // 获取CPU信息
    ipc.handle('system:getCpuInfo', () => {
      return SystemService.getCpuInfo()
    })

    // 获取网络接口信息
    ipc.handle('system:getNetworkInterfaces', () => {
      return SystemService.getNetworkInterfaces()
    })

    moduleErrorHandlers.ipc.info('系统信息处理器注册完成', 'registerSystemHandlers')
  }

  /**
   * 注册更新相关处理器
   */
  private registerUpdateHandlers(ipc: MainIPC): void {
    UpdateHandler.registerHandlers(ipc)
    moduleErrorHandlers.ipc.info('更新处理器注册完成', 'registerUpdateHandlers')
  }

  /**
   * 检查是否已注册
   */
  isHandlersRegistered(): boolean {
    return this.isRegistered
  }
}
