import type { IpcMainInvokeEvent } from 'electron'
import type { MainIPC } from '../../ipc'
import { UpdateService } from '../services/update-service'
import type { UpdateInfo } from '../services/update-service'

/**
 * 更新相关的IPC处理器
 */
export class UpdateHandler {
  /**
   * 注册更新相关的IPC处理器
   */
  static registerHandlers(ipc: MainIPC): void {
    // 检查更新
    ipc.handle('update:check', async (event: IpcMainInvokeEvent, silent = false) => {
      return await UpdateService.checkForUpdates(silent)
    })

    // 下载更新
    ipc.handle('update:download', async (event: IpcMainInvokeEvent) => {
      return await UpdateService.downloadUpdate()
    })

    // 安装更新
    ipc.handle('update:install', async (event: IpcMainInvokeEvent) => {
      return UpdateService.installUpdate()
    })

    // 获取更新信息
    ipc.handle('update:getInfo', async (event: IpcMainInvokeEvent): Promise<UpdateInfo> => {
      return UpdateService.getUpdateInfo()
    })

    // 获取当前版本
    ipc.handle('update:getCurrentVersion', async (event: IpcMainInvokeEvent): Promise<string> => {
      return UpdateService.getCurrentVersion()
    })

    // 设置更新服务器地址
    ipc.handle('update:setServerUrl', async (event: IpcMainInvokeEvent, url: string) => {
      return UpdateService.setUpdateServerUrl(url)
    })

    UpdateService.logInfo('更新IPC处理器注册完成', 'registerHandlers')
  }
}
