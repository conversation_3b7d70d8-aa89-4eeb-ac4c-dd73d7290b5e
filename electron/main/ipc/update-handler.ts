import type { IpcMainInvokeEvent } from 'electron'
import type { MainIPC } from '../../ipc'
import type { UpdateInfo } from '../services/update-service'
import { updateService } from '../services/update-service'
import { moduleErrorHandlers } from '../core/error-handler'

/**
 * 更新相关的IPC处理器
 */
export class UpdateHandler {
  /**
   * 注册更新相关的IPC处理器
   */
  static registerHandlers(ipc: MainIPC): void {
    // 检查更新
    ipc.handle('update:check', async (event: IpcMainInvokeEvent, silent = false) => {
      try {
        await updateService.checkForUpdates(silent)
        return { success: true }
      }
      catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        moduleErrorHandlers.main.error(`IPC检查更新失败: ${errorMessage}`, 'UpdateHandler')
        return { success: false, error: errorMessage }
      }
    })

    // 下载更新
    ipc.handle('update:download', async (event: IpcMainInvokeEvent) => {
      try {
        await updateService.downloadUpdate()
        return { success: true }
      }
      catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        moduleErrorHandlers.main.error(`IPC下载更新失败: ${errorMessage}`, 'UpdateHandler')
        return { success: false, error: errorMessage }
      }
    })

    // 安装更新
    ipc.handle('update:install', async (event: IpcMainInvokeEvent) => {
      try {
        updateService.installUpdate()
        return { success: true }
      }
      catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        moduleErrorHandlers.main.error(`IPC安装更新失败: ${errorMessage}`, 'UpdateHandler')
        return { success: false, error: errorMessage }
      }
    })

    // 获取更新信息
    ipc.handle('update:getInfo', async (event: IpcMainInvokeEvent): Promise<UpdateInfo> => {
      return updateService.getUpdateInfo()
    })

    // 获取当前版本
    ipc.handle('update:getCurrentVersion', async (event: IpcMainInvokeEvent): Promise<string> => {
      return updateService.getCurrentVersion()
    })

    // 设置更新服务器地址
    ipc.handle('update:setServerUrl', async (event: IpcMainInvokeEvent, url: string) => {
      try {
        updateService.setUpdateServerUrl(url)
        return { success: true }
      }
      catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        moduleErrorHandlers.main.error(`IPC设置更新服务器失败: ${errorMessage}`, 'UpdateHandler')
        return { success: false, error: errorMessage }
      }
    })

    moduleErrorHandlers.main.info('更新IPC处理器注册完成', 'UpdateHandler')
  }
}
