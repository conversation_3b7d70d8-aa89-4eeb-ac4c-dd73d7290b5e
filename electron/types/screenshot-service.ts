/**
 * 截图源接口
 */
export interface ScreenshotSource {
  /** 源名称 */
  name: string
  /** 源ID */
  id: string
  /** 缩略图（） */
  thumbnail: Electron.NativeImage | undefined
  /** 显示器ID */
  display_id: string
  /** 应用图标（base64格式） */
  appIcon: string | null
  /** 源类型 */
  type: 'window' | 'screen'
}

/**
 * 截图操作结果
 */
export interface ScreenshotResult {
  success: boolean
  error?: string
  data?: {
    sources: ScreenshotSource[]
  }
}
