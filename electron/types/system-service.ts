/**
 * 系统信息接口
 */
export interface SystemInfo {
  platform: string
  arch: string
  version: string
  totalMemory: number
  freeMemory: number
  cpuCount: number
  hostname: string
  userInfo: {
    username: string
    homedir: string
    shell?: string
  }
}

/**
 * 内存使用信息接口
 */
export interface MemoryUsage {
  total: number
  free: number
  used: number
  usagePercent: number
}

/**
 * CPU信息接口
 */
export interface CpuInfo {
  count: number
  model: string
  speed: number
}
