/**
 * DOM 就绪状态检测工具
 * 提供等待 DOM 加载完成的功能
 */

/**
 * 等待 DOM 加载完成
 *
 * @param condition 期望的文档加载状态数组，默认为 ['complete', 'interactive']
 * @returns Promise，当文档达到预期状态时解析
 */
export function domReady(condition: DocumentReadyState[] = ['complete', 'interactive']): Promise<boolean> {
  console.log('domReady', document.readyState)
  return new Promise((resolve) => {
    if (condition.includes(document.readyState)) {
      resolve(true)
    }
    else {
      const handler = () => {
        if (condition.includes(document.readyState)) {
          document.removeEventListener('readystatechange', handler)
          resolve(false)
        }
      }
      document.addEventListener('readystatechange', handler)
    }
  })
}

/**
 * 等待 DOM 完全加载完成（包括所有资源）
 */
export function domComplete(): Promise<boolean> {
  return domReady(['complete'])
}

/**
 * 等待 DOM 交互就绪（DOM 解析完成，但资源可能还在加载）
 */
export function domInteractive(): Promise<boolean> {
  return domReady(['interactive', 'complete'])
}

// 安全操作 DOM 的工具函数
const safeDOM = {
  /**
   * 安全地将子元素添加到父元素中，避免重复添加。
   *
   * 该方法首先检查父元素的子元素列表，确保目标子元素尚未存在，然后才执行添加操作。
   * 如果子元素已存在于父元素中，则不会重复添加，从而保持 DOM 结构的完整性。
   *
   * @param parent 父元素，类型为 HTMLElement，表示要添加子元素的目标容器。
   * @param child 子元素，类型为 HTMLElement，表示要添加到父元素中的新元素。
   * @returns 如果子元素未重复，则返回 appendChild 方法的执行结果（通常是子元素本身）；如果子元素已存在，则返回 undefined。
   */
  append(parent: HTMLElement, child: HTMLElement) {
    // 检查父元素的子元素列表中是否已包含目标子元素
    if (!Array.from(parent.children).find(e => e === child)) {
      return parent.appendChild(child)
    }
  },
  /**
   * 移除子元素，确保存在时才移除
   *
   * 此函数旨在从父元素中移除指定的子元素之前，先检查该子元素是否确实为父元素的子元素
   * 这样做可以避免尝试移除不存在的子元素时引发的错误
   *
   * @param parent 父元素，即我们要从中移除子元素的元素
   * @param child 要被移除的子元素
   * @returns 如果子元素被成功找到并移除，则返回被移除的子元素；如果子元素未被找到，则不返回任何值
   */
  // 移除子元素，确保存在时才移除
  remove(parent: HTMLElement, child: HTMLElement) {
    // 检查子元素是否存在于父元素的子元素列表中
    if (Array.from(parent.children).find(e => e === child)) {
      // 如果找到，则从父元素中移除该子元素
      return parent.removeChild(child)
    }
  },
}

/**
 * 检测是否应该显示loading
 * 区分应用初始化和页面刷新
 */
function shouldShowLoading(): boolean {
  const SESSION_REFRESH_KEY = 'app-session-refresh'

  try {
    // 检查是否为页面刷新
    const isPageRefresh = sessionStorage.getItem(SESSION_REFRESH_KEY)

    if (isPageRefresh) {
      // 如果是页面刷新，不显示loading
      console.log('[Loading] 检测到页面刷新，跳过loading')
      return false
    }

    // 标记当前会话已经加载过，防止刷新时显示loading
    sessionStorage.setItem(SESSION_REFRESH_KEY, 'true')

    // 应用初始化时显示loading
    console.log('[Loading] 检测到应用初始化，显示loading')
    return true
  }
  catch (error) {
    // 如果sessionStorage不可用，默认显示loading
    console.warn('无法访问sessionStorage，默认显示loading:', error)
    return true
  }
}

/**
 * 使用CSS和JavaScript创建一个加载动画
 * 该函数受到以下网站的加载动画启发：
 * https://tobiasahlin.com/spinkit
 * https://connoratherton.com/loaders
 * https://projects.lukehaas.me/css-loaders
 * https://matejkustec.github.io/SpinThatShit
 */
export function useLoading() {
  // 定义加载动画的CSS类名
  const className = `loaders-css__square-spin`
  // 定义加载动画的CSS样式
  const styleContent = `
  @keyframes square-spin {
    25% { transform: perspective(100px) rotateX(180deg) rotateY(0); }
    50% { transform: perspective(100px) rotateX(180deg) rotateY(180deg); }
    75% { transform: perspective(100px) rotateX(0) rotateY(180deg); }
    100% { transform: perspective(100px) rotateX(0) rotateY(0); }
  }
  .${className} > div {
    animation-fill-mode: both;
    width: 50px;
    height: 50px;
    background: #fff;
    animation: square-spin 3s 0s cubic-bezier(0.09, 0.57, 0.49, 0.9) infinite;
  }
  .app-loading-wrap {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #282c34;
    z-index: 9;
  }`
  // 创建style元素用于插入CSS样式
  const oStyle = document.createElement('style')
  // 创建div元素用于显示加载动画
  const oDiv = document.createElement('div')
  // 设置style元素的id和内容
  oStyle.id = 'app-loading-style'
  oStyle.innerHTML = styleContent
  // 设置div元素的类名和内容
  oDiv.className = 'app-loading-wrap'
  oDiv.innerHTML = `<div class="${className}"><div></div></div>`

  // 返回包含添加和移除加载动画的方法的对象
  return {
    /**
     * 添加加载动画
     * 在应用初始化时显示loading，刷新时不显示
     */
    appendLoading() {
      // 检测是否应该显示loading
      if (shouldShowLoading()) {
        console.log('[Loading] 显示loading动画')
        // 将style元素添加到文档头部
        safeDOM.append(document.head, oStyle)
        // 将div元素添加到文档主体
        safeDOM.append(document.body, oDiv)
      }
      else {
        console.log('[Loading] 跳过loading动画')
      }
    },
    /**
     * 移除加载动画
     */
    removeLoading() {
      console.log('[Loading] 移除loading动画')
      // 从文档头部移除style元素
      safeDOM.remove(document.head, oStyle)
      // 从文档主体移除div元素
      safeDOM.remove(document.body, oDiv)
    },
  }
}
