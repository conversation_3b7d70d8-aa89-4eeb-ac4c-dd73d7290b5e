import type { RendererIPC } from '../../ipc'
import { moduleErrorHandlers } from '../../main/core/error-handler'
import { EventService } from '../../render'
import { domReady, useLoading } from './dom-ready'
// 初始化加载动画功能
const { appendLoading, removeLoading } = useLoading()
/**
 * IPC 系统初始化配置
 */
export interface IPCSetupOptions {
  /** 是否启用调试日志 */
  enableDebug?: boolean
  /** 自定义初始化回调 */
  onInitialized?: () => void
  /** 自定义清理回调 */
  onDisposed?: () => void
}

/**
 * 初始化 IPC 系统
 *
 * @param rendererIPC 渲染进程 IPC 实例
 * @param options 初始化选项
 */
export async function setupIPC(rendererIPC: RendererIPC, options: IPCSetupOptions = {}): Promise<void> {
  moduleErrorHandlers.main.info('主进程初始化完成，通知渲染进程应用已初始化', 'initialize')
  const { enableDebug = false, onInitialized, onDisposed } = options
  await domReady()
  appendLoading()

  // 初始化渲染进程 IPC
  rendererIPC.initialize()

  // 使用封装的服务函数注册移除加载动画事件监听器
  EventService.registerRemoveLoadingListener(rendererIPC, () => {
    removeLoading()
  })

  // 执行自定义初始化回调
  onInitialized?.()
  // 页面卸载时清理 IPC 资源
  window.addEventListener('beforeunload', () => {
    rendererIPC.dispose()
    onDisposed?.()
  })

  if (enableDebug) {
    window.console.log('[PreloadScript] IPC system initialized')
  }
}

/**
 * 注册业务相关的 IPC 处理器
 *
 * @param rendererIPC 渲染进程 IPC 实例
 * @param handlers 处理器映射
 */
export function registerIPCHandlers(
  rendererIPC: RendererIPC,
  handlers: Record<string, (data: any) => Promise<any> | any>,
): void {
  Object.entries(handlers).forEach(([channel, handler]) => {
    rendererIPC.handle(channel, handler)
  })
}
