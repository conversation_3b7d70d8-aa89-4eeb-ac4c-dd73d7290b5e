import { contextBridge, ipc<PERSON>enderer } from 'electron'
import { RendererIPC, businessIPC, presets } from '../ipc'

// --------- 工具函数导入 ---------
import { setupIPC } from './utils/ipc-setup'

// 创建渲染进程 IPC 实例
const rendererIPC = new RendererIPC(ipcRenderer, {
  ...presets.production,
  enableDebug: process.env.NODE_ENV === 'development',
})

// --------- 暴露优化的 IPC API ---------
contextBridge.exposeInMainWorld('optimizedIPC', {
  /**
   * 发送请求到主进程（优化版本）
   */
  invoke: <T = any, R = any>(channel: string, data: T, timeout?: number): Promise<R> => {
    return rendererIPC.invoke<T, R>(channel, data, timeout)
  },

  /**
   * 监听来自主进程的事件（优化版本）
   */
  on: <T = any>(channel: string, listener: (data: T) => void) => {
    return rendererIPC.on(channel, listener)
  },

  /**
   * 移除事件监听器（优化版本）
   */
  off: <T = any>(channel: string, listener?: (data: T) => void) => {
    return rendererIPC.off(channel, listener)
  },

  /**
   * 发送事件到主进程（优化版本）
   */
  emit: <T = any>(channel: string, data: T) => {
    return rendererIPC.emit(channel, data)
  },

  /**
   * 注册消息处理器
   */
  handle: <T = any, R = any>(channel: string, handler: (data: T) => Promise<R> | R) => {
    return rendererIPC.handle(channel, handler)
  },

  /**
   * 取消注册消息处理器
   */
  unhandle: (channel: string) => {
    return rendererIPC.unhandle(channel)
  },

  /**
   * 发送事件到其他渲染进程
   */
  emitToRenderer: <T = any>(channel: string, data: T) => {
    return rendererIPC.emitToRenderer(channel, data)
  },

  /**
   * 监听来自其他渲染进程的事件
   */
  onRenderer: <T = any>(channel: string, listener: (data: T) => void) => {
    return rendererIPC.onRenderer(channel, listener)
  },

  /**
   * 移除渲染进程间事件监听器
   */
  offRenderer: <T = any>(channel: string, listener?: (data: T) => void) => {
    return rendererIPC.offRenderer(channel, listener)
  },
})

// --------- 暴露类型安全的业务 IPC API ---------
contextBridge.exposeInMainWorld('businessIPC', {
  /**
   * 类型安全的业务请求调用
   */
  invoke: <K extends keyof import('../ipc').BusinessIPCInterface>(
    channel: K,
    data: Parameters<import('../ipc').BusinessIPCInterface[K]>[0],
  ): Promise<ReturnType<import('../ipc').BusinessIPCInterface[K]>> => {
    return businessIPC.invoke(rendererIPC, channel, data)
  },

  /**
   * 类型安全的业务事件发送
   */
  emit: <K extends keyof import('../ipc').BusinessIPCInterface>(
    channel: K,
    data: Parameters<import('../ipc').BusinessIPCInterface[K]>[0],
  ) => {
    return businessIPC.emit(rendererIPC, channel, data)
  },

  /**
   * 类型安全的业务事件监听
   */
  on: <K extends keyof import('../ipc').BusinessIPCInterface>(
    channel: K,
    listener: import('../ipc').BusinessIPCInterface[K],
  ) => {
    return businessIPC.on(rendererIPC, channel, listener)
  },
})

// --------- 初始化优化的 IPC 系统 ---------
// 使用抽离的工具函数初始化 IPC 系统
setupIPC(rendererIPC, {
  enableDebug: process.env.NODE_ENV === 'development',
  onInitialized: () => {
    // 可以在这里添加应用特定的 IPC 处理逻辑
    // 例如：注册自定义处理器、设置事件监听器等
  },
  onDisposed: () => {
    // IPC 系统清理完成后的回调
  },
})
