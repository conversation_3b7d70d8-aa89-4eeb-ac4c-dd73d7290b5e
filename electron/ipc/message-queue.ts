/**
 * IPC 消息队列和批处理管理器
 */

import type {
  BatchMessage,
  EventMessage,
  IPCMessage,
  IPCOptions,
  RequestMessage,
  ResponseMessage,
} from './types'
import { DEFAULT_IPC_OPTIONS, MessagePriority, MessageType } from './types'
import { MESSAGE_ID_PREFIX, PERFORMANCE_METRICS } from './constants'

/**
 * 消息队列项
 */
interface QueueItem {
  message: IPCMessage
  timestamp: number
  retryCount: number
}

/**
 * 批处理统计信息
 */
interface BatchStats {
  totalMessages: number
  batchCount: number
  averageBatchSize: number
  lastBatchTime: number
}

/**
 * 消息队列管理器
 */
export class MessageQueue {
  private queue: Map<MessagePriority, QueueItem[]> = new Map()
  private batchTimer: NodeJS.Timeout | null = null
  private options: IPCOptions
  private stats: BatchStats = {
    totalMessages: 0,
    batchCount: 0,
    averageBatchSize: 0,
    lastBatchTime: 0,
  }

  // 事件回调
  private onBatchReady?: (batch: BatchMessage) => void
  private onQueueOverflow?: (queueSize: number) => void

  constructor(options: Partial<IPCOptions> = {}) {
    this.options = { ...DEFAULT_IPC_OPTIONS, ...options }
    this.initializeQueue()
  }

  /**
   * 初始化队列
   */
  private initializeQueue(): void {
    // 为每个优先级创建队列
    Object.values(MessagePriority).forEach((priority) => {
      if (typeof priority === 'number') {
        this.queue.set(priority, [])
      }
    })
  }

  /**
   * 添加消息到队列
   */
  enqueue(message: IPCMessage): void {
    const queueItem: QueueItem = {
      message,
      timestamp: Date.now(),
      retryCount: 0,
    }

    const priorityQueue = this.queue.get(message.priority)
    if (!priorityQueue) {
      throw new Error(`Invalid message priority: ${message.priority}`)
    }

    priorityQueue.push(queueItem)
    this.stats.totalMessages++

    // 检查队列大小
    this.checkQueueSize()

    // 启动批处理定时器
    this.startBatchTimer()

    // 如果是高优先级消息，立即处理
    if (message.priority >= MessagePriority.HIGH) {
      this.processBatch()
    }
  }

  /**
   * 从队列中移除消息
   */
  dequeue(_priority: MessagePriority = MessagePriority.URGENT): QueueItem | null {
    // 按优先级从高到低处理
    for (let p = MessagePriority.URGENT; p >= MessagePriority.LOW; p--) {
      const queue = this.queue.get(p)
      if (queue && queue.length > 0) {
        return queue.shift() || null
      }
    }
    return null
  }

  /**
   * 获取队列大小
   */
  size(): number {
    let total = 0
    this.queue.forEach((queue) => {
      total += queue.length
    })
    return total
  }

  /**
   * 清空队列
   */
  clear(): void {
    this.queue.forEach(queue => queue.length = 0)
    this.clearBatchTimer()
  }

  /**
   * 设置批处理回调
   */
  onBatch(callback: (batch: BatchMessage) => void): void {
    this.onBatchReady = callback
  }

  /**
   * 设置队列溢出回调
   */
  onOverflow(callback: (queueSize: number) => void): void {
    this.onQueueOverflow = callback
  }

  /**
   * 启动批处理定时器
   */
  private startBatchTimer(): void {
    if (this.batchTimer) {
      return
    }

    this.batchTimer = setTimeout(() => {
      this.processBatch()
    }, this.options.batchTimeout)
  }

  /**
   * 清除批处理定时器
   */
  private clearBatchTimer(): void {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer)
      this.batchTimer = null
    }
  }

  /**
   * 处理批次
   */
  private processBatch(): void {
    this.clearBatchTimer()

    const batch: IPCMessage[] = []
    let processedCount = 0

    // 收集消息直到达到批处理大小
    while (batch.length < this.options.batchSize && processedCount < this.size()) {
      const item = this.dequeue()
      if (!item)
        break

      batch.push(item.message)
      processedCount++
    }

    if (batch.length === 0) {
      return
    }

    // 创建批处理消息
    const batchMessage: BatchMessage = {
      id: this.generateBatchId(),
      type: MessageType.BATCH,
      channel: '__batch__',
      timestamp: Date.now(),
      priority: this.getHighestPriority(batch),
      messages: batch.filter(msg => msg.type !== MessageType.BATCH) as Array<RequestMessage | ResponseMessage | EventMessage>,
    }

    // 更新统计信息
    this.updateStats(batch.length)

    // 触发批处理回调
    if (this.onBatchReady) {
      this.onBatchReady(batchMessage)
    }

    // 如果还有消息，继续处理
    if (this.size() > 0) {
      this.startBatchTimer()
    }
  }

  /**
   * 检查队列大小并触发警告
   */
  private checkQueueSize(): void {
    const size = this.size()

    if (size >= PERFORMANCE_METRICS.QUEUE_SIZE_ERROR) {
      console.error(`IPC Queue size critical: ${size}`)
      if (this.onQueueOverflow) {
        this.onQueueOverflow(size)
      }
    }
    else if (size >= PERFORMANCE_METRICS.QUEUE_SIZE_WARNING) {
      console.warn(`IPC Queue size warning: ${size}`)
    }
  }

  /**
   * 获取批次中的最高优先级
   */
  private getHighestPriority(messages: IPCMessage[]): MessagePriority {
    return messages.reduce((highest, msg) =>
      Math.max(highest, msg.priority), MessagePriority.LOW)
  }

  /**
   * 生成批处理 ID
   */
  private generateBatchId(): string {
    return `${MESSAGE_ID_PREFIX.BATCH}${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 更新统计信息
   */
  private updateStats(batchSize: number): void {
    this.stats.batchCount++
    this.stats.lastBatchTime = Date.now()
    this.stats.averageBatchSize
      = (this.stats.averageBatchSize * (this.stats.batchCount - 1) + batchSize) / this.stats.batchCount
  }

  /**
   * 获取统计信息
   */
  getStats(): BatchStats {
    return { ...this.stats }
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalMessages: 0,
      batchCount: 0,
      averageBatchSize: 0,
      lastBatchTime: 0,
    }
  }
}

/**
 * 事件合并器
 * 用于合并相同类型的事件，避免重复处理
 */
export class EventMerger {
  private eventMap: Map<string, EventMessage> = new Map()
  private mergeTimer: NodeJS.Timeout | null = null
  private mergeDelay: number

  constructor(mergeDelay: number = 16) { // ~60fps
    this.mergeDelay = mergeDelay
  }

  /**
   * 添加事件进行合并
   */
  addEvent(event: EventMessage, onMerged: (events: EventMessage[]) => void): void {
    const key = `${event.channel}_${event.type}`

    // 如果是相同的事件，替换旧的（保留最新的）
    this.eventMap.set(key, event)

    // 启动合并定时器
    if (!this.mergeTimer) {
      this.mergeTimer = setTimeout(() => {
        this.flushEvents(onMerged)
      }, this.mergeDelay)
    }
  }

  /**
   * 刷新所有待合并的事件
   */
  private flushEvents(onMerged: (events: EventMessage[]) => void): void {
    if (this.eventMap.size === 0) {
      return
    }

    const events = Array.from(this.eventMap.values())
    this.eventMap.clear()
    this.mergeTimer = null

    onMerged(events)
  }

  /**
   * 清空所有待合并的事件
   */
  clear(): void {
    this.eventMap.clear()
    if (this.mergeTimer) {
      clearTimeout(this.mergeTimer)
      this.mergeTimer = null
    }
  }
}
