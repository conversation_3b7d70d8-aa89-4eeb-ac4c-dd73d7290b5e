# 优化的 Electron IPC 库

这是一个基于 Electron 原生 IPC 接口封装的高性能 IPC 通信库，具有以下特征：

## 主要特性

1. **异步通信** - 所有操作都是异步的，避免阻塞主线程
2. **消息合并** - 自动批量处理事件推送，提高性能
3. **JSON 序列化** - 直接传递 JSON 字符串，避免 Electron 内部序列化的复杂性
4. **统一 API** - 支持主进程与渲染进程、渲染进程与渲染进程之间的双向通信
5. **类型安全** - 完整的 TypeScript 类型支持
6. **错误处理** - 完善的错误处理和超时机制
7. **性能监控** - 内置性能监控和调试功能

## 快速开始

### 在主进程中使用

```typescript
import { mainIPC, registerBusinessHandlers } from './ipc'

// 初始化 IPC 系统
mainIPC.initialize()

// 注册预定义的业务处理器
registerBusinessHandlers(mainIPC)

// 注册自定义处理器
mainIPC.handle('custom:getData', async (params) => {
  // 处理逻辑
  return { data: 'some data', timestamp: Date.now() }
})

// 监听事件
mainIPC.on('user:action', (data) => {
  console.log('User action:', data)
})

// 发送事件到渲染进程
mainIPC.emit('app:notification', {
  message: 'Hello from main process',
  type: 'info'
})

// 发送请求到指定渲染进程
const result = await mainIPC.invoke(webContentsId, 'renderer:getData', { id: 123 })
```

### 在渲染进程中使用

```typescript
// 使用优化的 IPC API（在预加载脚本中已暴露）
declare global {
  interface Window {
    optimizedIPC: {
      invoke: <T, R>(channel: string, data: T, timeout?: number) => Promise<R>
      on: <T>(channel: string, listener: (data: T) => void) => void
      off: <T>(channel: string, listener?: (data: T) => void) => void
      emit: <T>(channel: string, data: T) => void
      handle: <T, R>(channel: string, handler: (data: T) => Promise<R> | R) => void
      unhandle: (channel: string) => void
      emitToRenderer: <T>(channel: string, data: T) => void
      onRenderer: <T>(channel: string, listener: (data: T) => void) => void
      offRenderer: <T>(channel: string, listener?: (data: T) => void) => void
    }
    businessIPC: {
      invoke: <K extends keyof BusinessIPCInterface>(
        channel: K,
        data: Parameters<BusinessIPCInterface[K]>[0]
      ) => Promise<ReturnType<BusinessIPCInterface[K]>>
      emit: <K extends keyof BusinessIPCInterface>(
        channel: K,
        data: Parameters<BusinessIPCInterface[K]>[0]
      ) => void
      on: <K extends keyof BusinessIPCInterface>(
        channel: K,
        listener: BusinessIPCInterface[K]
      ) => void
    }
  }
}

// 发送请求到主进程
const result = await window.optimizedIPC.invoke('custom:getData', { id: 123 })

// 监听来自主进程的事件
window.optimizedIPC.on('app:notification', (data) => {
  console.log('Notification:', data)
})

// 注册处理器
window.optimizedIPC.handle('renderer:getData', async (params) => {
  return { data: 'renderer data', params }
})

// 发送事件到主进程
window.optimizedIPC.emit('user:action', { action: 'click', target: 'button' })

// 渲染进程间通信
window.optimizedIPC.emitToRenderer('renderer:message', {
  from: 'renderer1',
  message: 'Hello other renderers'
})

window.optimizedIPC.onRenderer('renderer:message', (data) => {
  console.log('Message from other renderer:', data)
})
```

### 使用类型安全的业务 API

```typescript
// 类型安全的业务调用
const loginResult = await window.businessIPC.invoke('user:login', {
  username: 'admin',
  password: 'password'
})

const userInfo = await window.businessIPC.invoke('user:info', 123)

const fileContent = await window.businessIPC.invoke('file:read', '/path/to/file.txt')

// 类型安全的事件监听
window.businessIPC.on('app:theme-change', (theme) => {
  // theme 的类型会被自动推断为 'light' | 'dark' | 'auto'
  document.body.className = `theme-${theme}`
})
```

## 配置选项

```typescript
import { createMainIPC, presets } from './ipc'

// 使用预设配置
const mainIPC = createMainIPC(presets.highPerformance)

// 自定义配置
const mainIPC = createMainIPC({
  batchSize: 20, // 批处理大小
  batchTimeout: 16, // 批处理超时时间（毫秒）
  defaultTimeout: 5000, // 默认请求超时时间
  maxRetries: 3, // 最大重试次数
  enableCompression: true, // 启用压缩
  enableDebug: false // 启用调试模式
})
```

## 预设配置

- `presets.highPerformance` - 高性能配置，适用于需要高频通信的场景
- `presets.development` - 开发调试配置，启用详细日志和较长超时时间
- `presets.production` - 生产环境配置，平衡性能和稳定性

## 错误处理

```typescript
try {
  const result = await window.optimizedIPC.invoke('some:channel', data)
}
catch (error) {
  if (error instanceof IPCTimeoutError) {
    console.error('Request timed out')
  }
  else if (error instanceof IPCSerializationError) {
    console.error('Serialization failed')
  }
  else if (error instanceof IPCChannelNotFoundError) {
    console.error('Channel not found')
  }
  else {
    console.error('Unknown IPC error:', error)
  }
}
```

## 性能监控

库内置了性能监控功能，会自动记录：

- 消息处理时间
- 队列大小
- 批处理统计
- 内存使用情况

在开发模式下，这些信息会输出到控制台。

## 最佳实践

1. **使用批处理** - 对于高频事件，让库自动进行批处理以提高性能
2. **合理设置超时** - 根据业务需求设置合适的超时时间
3. **错误处理** - 始终处理可能的 IPC 错误
4. **资源清理** - 在组件卸载时移除事件监听器
5. **类型安全** - 尽量使用类型安全的业务 API

## 架构说明

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Renderer 1    │    │   Main Process  │    │   Renderer 2    │
│                 │    │                 │    │                 │
│  RendererIPC    │◄──►│    MainIPC      │◄──►│  RendererIPC    │
│                 │    │                 │    │                 │
│  - MessageQueue │    │  - MessageQueue │    │  - MessageQueue │
│  - EventMerger  │    │  - EventMerger  │    │  - EventMerger  │
│  - Serializer   │    │  - Serializer   │    │  - Serializer   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

每个进程都有自己的消息队列和事件合并器，确保高效的消息处理和传输。

## 实现完成状态

✅ **已完成的功能：**

1. **核心架构**
   - 完整的类型定义系统 (`types.ts`)
   - 常量和配置管理 (`constants.ts`)
   - JSON 序列化器 (`serializer.ts`)
   - 消息队列和批处理 (`message-queue.ts`)

2. **主进程 IPC** (`main-ipc.ts`)
   - 异步消息处理
   - 批量消息支持
   - 事件合并
   - 渲染进程管理
   - 错误处理和超时机制

3. **渲染进程 IPC** (`renderer-ipc.ts`)
   - 与主进程双向通信
   - 渲染进程间通信
   - 事件监听和处理
   - 自动资源清理

4. **预加载脚本集成** (`preload/index.ts`)
   - 安全的 API 暴露
   - 优化的 IPC 接口
   - 类型安全的业务 API
   - 自动初始化

5. **主进程集成** (`main/index.ts`)
   - IPC 系统初始化
   - 业务处理器注册
   - 自定义处理器示例
   - 资源清理

6. **开发工具**
   - 完整的使用示例 (`example.ts`)
   - 测试套件 (`test.ts`)
   - 详细的文档和说明

## 技术特性验证

✅ **异步通信**：所有 IPC 操作都基于 Promise，避免阻塞
✅ **消息合并**：自动批处理高频事件，提升性能
✅ **JSON 序列化**：直接控制序列化过程，支持复杂数据类型
✅ **统一 API**：主进程、渲染进程使用相同的接口设计
✅ **类型安全**：完整的 TypeScript 类型支持
✅ **错误处理**：完善的错误分类和处理机制
✅ **性能优化**：批处理、事件合并、内存管理

## 使用方式

### 在主进程中
```typescript
import { mainIPC, registerBusinessHandlers } from './ipc'

// 初始化
mainIPC.initialize()
registerBusinessHandlers(mainIPC)

// 自定义处理器
mainIPC.handle('custom:api', async (data) => {
  return { result: 'processed', data }
})
```

### 在渲染进程中
```typescript
// 使用优化的 IPC API
const result = await window.optimizedIPC.invoke('custom:api', { test: 'data' })

// 使用类型安全的业务 API
const user = await window.businessIPC.invoke('user:login', {
  username: 'admin',
  password: 'password'
})
```

## 下一步建议

1. **生产环境测试**：在实际项目中测试性能和稳定性
2. **监控集成**：添加详细的性能监控和日志记录
3. **文档完善**：根据实际使用情况完善 API 文档
4. **单元测试**：添加更全面的单元测试覆盖
5. **性能调优**：根据实际使用场景优化批处理参数

这个 IPC 库已经具备了生产环境使用的基础功能，可以显著提升 Electron 应用的 IPC 通信性能和开发体验。
