/**
 * IPC 连接池管理器
 * 优化渲染进程间通信的连接管理
 */

import type { EventListener } from './types'

// 连接状态
export enum ConnectionStatus {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  ERROR = 'error',
}

// 连接信息
export interface ConnectionInfo {
  id: string
  webContentsId: number
  status: ConnectionStatus
  lastSeen: number
  channels: Set<string>
  messageCount: number
  errorCount: number
}

// 连接池配置
export interface ConnectionPoolOptions {
  maxConnections: number
  connectionTimeout: number
  heartbeatInterval: number
  maxIdleTime: number
  enableHeartbeat: boolean
}

// 默认配置
const DEFAULT_OPTIONS: ConnectionPoolOptions = {
  maxConnections: 50,
  connectionTimeout: 5000,
  heartbeatInterval: 30000, // 30秒
  maxIdleTime: 300000, // 5分钟
  enableHeartbeat: true,
}

/**
 * 连接池管理器
 */
export class ConnectionPool {
  private connections: Map<number, ConnectionInfo> = new Map()
  private channelSubscriptions: Map<string, Set<number>> = new Map()
  private heartbeatTimer?: NodeJS.Timeout
  private cleanupTimer?: NodeJS.Timeout
  private options: ConnectionPoolOptions

  // 事件监听器
  private eventListeners: Map<string, Set<EventListener>> = new Map()

  constructor(options: Partial<ConnectionPoolOptions> = {}) {
    this.options = { ...DEFAULT_OPTIONS, ...options }
    this.startHeartbeat()
    this.startCleanup()
  }

  /**
   * 注册连接
   */
  registerConnection(webContentsId: number, channels: string[] = []): void {
    const existing = this.connections.get(webContentsId)

    if (existing) {
      // 更新现有连接
      existing.status = ConnectionStatus.CONNECTED
      existing.lastSeen = Date.now()
      channels.forEach(channel => existing.channels.add(channel))
    }
    else {
      // 检查连接数限制
      if (this.connections.size >= this.options.maxConnections) {
        this.removeOldestConnection()
      }

      // 创建新连接
      const connection: ConnectionInfo = {
        id: this.generateConnectionId(),
        webContentsId,
        status: ConnectionStatus.CONNECTED,
        lastSeen: Date.now(),
        channels: new Set(channels),
        messageCount: 0,
        errorCount: 0,
      }

      this.connections.set(webContentsId, connection)
    }

    // 更新通道订阅
    this.updateChannelSubscriptions(webContentsId, channels)
    this.emit('connection:registered', { webContentsId, channels })
  }

  /**
   * 注销连接
   */
  unregisterConnection(webContentsId: number): void {
    const connection = this.connections.get(webContentsId)
    if (connection) {
      // 清理通道订阅
      connection.channels.forEach((channel) => {
        const subscribers = this.channelSubscriptions.get(channel)
        if (subscribers) {
          subscribers.delete(webContentsId)
          if (subscribers.size === 0) {
            this.channelSubscriptions.delete(channel)
          }
        }
      })

      this.connections.delete(webContentsId)
      this.emit('connection:unregistered', { webContentsId })
    }
  }

  /**
   * 更新连接活跃状态
   */
  updateConnectionActivity(webContentsId: number): void {
    const connection = this.connections.get(webContentsId)
    if (connection) {
      connection.lastSeen = Date.now()
      connection.messageCount++
    }
  }

  /**
   * 记录连接错误
   */
  recordConnectionError(webContentsId: number, error: string): void {
    const connection = this.connections.get(webContentsId)
    if (connection) {
      connection.errorCount++
      connection.status = ConnectionStatus.ERROR
      this.emit('connection:error', { webContentsId, error })
    }
  }

  /**
   * 获取通道的所有订阅者
   */
  getChannelSubscribers(channel: string): number[] {
    const subscribers = this.channelSubscriptions.get(channel)
    return subscribers ? Array.from(subscribers) : []
  }

  /**
   * 获取连接信息
   */
  getConnection(webContentsId: number): ConnectionInfo | undefined {
    return this.connections.get(webContentsId)
  }

  /**
   * 获取所有连接
   */
  getAllConnections(): ConnectionInfo[] {
    return Array.from(this.connections.values())
  }

  /**
   * 获取活跃连接数
   */
  getActiveConnectionCount(): number {
    return Array.from(this.connections.values())
      .filter(conn => conn.status === ConnectionStatus.CONNECTED).length
  }

  /**
   * 检查连接是否存在
   */
  hasConnection(webContentsId: number): boolean {
    return this.connections.has(webContentsId)
  }

  /**
   * 广播消息到所有订阅者
   */
  broadcast(channel: string, data: any, excludeWebContentsId?: number): number[] {
    const subscribers = this.getChannelSubscribers(channel)
    const delivered: number[] = []

    subscribers.forEach((webContentsId) => {
      if (webContentsId !== excludeWebContentsId) {
        const connection = this.connections.get(webContentsId)
        if (connection && connection.status === ConnectionStatus.CONNECTED) {
          try {
            this.emit('message:broadcast', { webContentsId, channel, data })
            delivered.push(webContentsId)
            this.updateConnectionActivity(webContentsId)
          }
          catch (error) {
            this.recordConnectionError(webContentsId, String(error))
          }
        }
      }
    })

    return delivered
  }

  /**
   * 添加事件监听器
   */
  on(event: string, listener: EventListener): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set())
    }
    this.eventListeners.get(event)!.add(listener)
  }

  /**
   * 移除事件监听器
   */
  off(event: string, listener?: EventListener): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      if (listener) {
        listeners.delete(listener)
        if (listeners.size === 0) {
          this.eventListeners.delete(event)
        }
      }
      else {
        this.eventListeners.delete(event)
      }
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach((listener) => {
        try {
          listener(data, { channel: event } as any)
        }
        catch (error) {
          console.error(`Connection pool event listener error:`, error)
        }
      })
    }
  }

  /**
   * 更新通道订阅
   */
  private updateChannelSubscriptions(webContentsId: number, channels: string[]): void {
    channels.forEach((channel) => {
      if (!this.channelSubscriptions.has(channel)) {
        this.channelSubscriptions.set(channel, new Set())
      }
      this.channelSubscriptions.get(channel)!.add(webContentsId)
    })
  }

  /**
   * 移除最旧的连接
   */
  private removeOldestConnection(): void {
    if (this.connections.size === 0) {
      return
    }

    let oldestConnection: ConnectionInfo | undefined
    let oldestTime = Number.MAX_SAFE_INTEGER

    this.connections.forEach((connection) => {
      if (connection.lastSeen < oldestTime) {
        oldestTime = connection.lastSeen
        oldestConnection = connection
      }
    })

    if (oldestConnection) {
      this.unregisterConnection(oldestConnection.webContentsId)
    }
  }

  /**
   * 启动心跳检测
   */
  private startHeartbeat(): void {
    if (!this.options.enableHeartbeat)
      return

    this.heartbeatTimer = setInterval(() => {
      this.performHeartbeat()
    }, this.options.heartbeatInterval)
  }

  /**
   * 执行心跳检测
   */
  private performHeartbeat(): void {
    const now = Date.now()
    const deadConnections: number[] = []

    this.connections.forEach((connection, webContentsId) => {
      const timeSinceLastSeen = now - connection.lastSeen

      if (timeSinceLastSeen > this.options.maxIdleTime) {
        deadConnections.push(webContentsId)
      }
      else {
        // 发送心跳
        this.emit('heartbeat:ping', { webContentsId })
      }
    })

    // 清理死连接
    deadConnections.forEach((webContentsId) => {
      this.unregisterConnection(webContentsId)
    })
  }

  /**
   * 启动清理定时器
   */
  private startCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.performCleanup()
    }, 60000) // 每分钟清理一次
  }

  /**
   * 执行清理
   */
  private performCleanup(): void {
    // 清理空的通道订阅
    this.channelSubscriptions.forEach((subscribers, channel) => {
      if (subscribers.size === 0) {
        this.channelSubscriptions.delete(channel)
      }
    })
  }

  /**
   * 生成连接 ID
   */
  private generateConnectionId(): string {
    return `conn_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 获取连接池统计信息
   */
  getStats() {
    const connections = this.getAllConnections()
    const activeConnections = connections.filter(c => c.status === ConnectionStatus.CONNECTED)

    return {
      totalConnections: connections.length,
      activeConnections: activeConnections.length,
      totalChannels: this.channelSubscriptions.size,
      totalMessages: connections.reduce((sum, c) => sum + c.messageCount, 0),
      totalErrors: connections.reduce((sum, c) => sum + c.errorCount, 0),
      averageMessagesPerConnection: connections.length > 0
        ? connections.reduce((sum, c) => sum + c.messageCount, 0) / connections.length
        : 0,
    }
  }

  /**
   * 销毁连接池
   */
  dispose(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = undefined
    }

    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }

    this.connections.clear()
    this.channelSubscriptions.clear()
    this.eventListeners.clear()
  }
}

// 默认连接池实例
export const defaultConnectionPool = new ConnectionPool()
