/**
 * IPC 通信类型定义
 */

// 消息类型枚举
export enum MessageType {
  REQUEST = 'request',
  RESPONSE = 'response',
  EVENT = 'event',
  BATCH = 'batch',
}

// 消息优先级
export enum MessagePriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  URGENT = 3,
}

// 基础消息接口
export interface BaseMessage {
  id: string
  type: MessageType
  channel: string
  timestamp: number
  priority: MessagePriority
}

// 请求消息
export interface RequestMessage extends BaseMessage {
  type: MessageType.REQUEST
  data: string // JSON 字符串
  timeout?: number
}

// 响应消息
export interface ResponseMessage extends BaseMessage {
  type: MessageType.RESPONSE
  requestId: string
  success: boolean
  data?: string // JSON 字符串
  error?: string
}

// 事件消息
export interface EventMessage extends BaseMessage {
  type: MessageType.EVENT
  data: string // JSON 字符串
}

// 批量消息
export interface BatchMessage extends BaseMessage {
  type: MessageType.BATCH
  messages: Array<RequestMessage | ResponseMessage | EventMessage>
}

// 联合消息类型
export type IPCMessage = RequestMessage | ResponseMessage | EventMessage | BatchMessage

// 消息处理器类型
export type MessageHandler<T = any> = (data: T, message: BaseMessage) => Promise<any> | any

// 事件监听器类型
export type EventListener<T = any> = (data: T, message: EventMessage) => void

// IPC 配置选项
export interface IPCOptions {
  // 批处理配置
  batchSize: number // 批处理大小
  batchTimeout: number // 批处理超时时间（毫秒）

  // 请求配置
  defaultTimeout: number // 默认请求超时时间（毫秒）
  maxRetries: number // 最大重试次数

  // 性能配置
  enableCompression: boolean // 是否启用压缩
  enableDebug: boolean // 是否启用调试模式
}

// 默认配置
export const DEFAULT_IPC_OPTIONS: IPCOptions = {
  batchSize: 10,
  batchTimeout: 16, // ~60fps
  defaultTimeout: 5000,
  maxRetries: 3,
  enableCompression: false,
  enableDebug: false,
}

// 错误类型
export class IPCError extends Error {
  constructor(
    message: string,
    public code: string,
    public originalError?: Error,
  ) {
    super(message)
    this.name = 'IPCError'
  }
}

// 超时错误
export class IPCTimeoutError extends IPCError {
  constructor(timeout: number) {
    super(`IPC request timed out after ${timeout}ms`, 'TIMEOUT')
  }
}

// 序列化错误
export class IPCSerializationError extends IPCError {
  constructor(originalError: Error) {
    super('Failed to serialize/deserialize IPC message', 'SERIALIZATION', originalError)
  }
}

// 通道未找到错误
export class IPCChannelNotFoundError extends IPCError {
  constructor(channel: string) {
    super(`IPC channel '${channel}' not found`, 'CHANNEL_NOT_FOUND')
  }
}
