/**
 * IPC 性能监控器
 * 提供详细的性能分析和监控功能
 */

import type { IPCMessage } from './types'
import { PERFORMANCE_METRICS } from './constants'

// 性能指标接口
export interface PerformanceMetrics {
  // 消息统计
  totalMessages: number
  messagesPerSecond: number
  averageMessageSize: number

  // 延迟统计
  averageLatency: number
  minLatency: number
  maxLatency: number
  p95Latency: number
  p99Latency: number

  // 吞吐量统计
  bytesPerSecond: number
  messagesInQueue: number

  // 错误统计
  errorRate: number
  timeoutRate: number

  // 内存使用
  memoryUsage: number
  peakMemoryUsage: number
}

// 消息记录
interface MessageRecord {
  id: string
  type: string
  channel: string
  size: number
  timestamp: number
  latency?: number
  error?: string
}

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
  private messageRecords: MessageRecord[] = []
  private latencyRecords: number[] = []
  private errorCount = 0
  private timeoutCount = 0
  private startTime = Date.now()
  private peakMemory = 0
  private isEnabled = false

  // 配置选项
  private maxRecords = 10000 // 最大记录数量
  private cleanupInterval = 60000 // 清理间隔（毫秒）
  private cleanupTimer?: NodeJS.Timeout

  constructor(enabled = false) {
    this.isEnabled = enabled
    if (enabled) {
      this.startCleanupTimer()
    }
  }

  /**
   * 启用监控
   */
  enable(): void {
    this.isEnabled = true
    this.startCleanupTimer()
  }

  /**
   * 禁用监控
   */
  disable(): void {
    this.isEnabled = false
    this.stopCleanupTimer()
  }

  /**
   * 记录消息发送
   */
  recordMessageSent(message: IPCMessage, size: number): void {
    if (!this.isEnabled)
      return

    const record: MessageRecord = {
      id: message.id,
      type: message.type,
      channel: message.channel,
      size,
      timestamp: Date.now(),
    }

    this.messageRecords.push(record)
    this.updateMemoryUsage()
    this.cleanupOldRecords()
  }

  /**
   * 记录消息接收（计算延迟）
   */
  recordMessageReceived(messageId: string, error?: string): void {
    if (!this.isEnabled)
      return

    const record = this.messageRecords.find(r => r.id === messageId)
    if (record) {
      const latency = Date.now() - record.timestamp
      record.latency = latency
      record.error = error

      if (error) {
        this.errorCount++
        if (error.includes('timeout')) {
          this.timeoutCount++
        }
      }
      else {
        this.latencyRecords.push(latency)

        // 检查慢消息
        if (latency > PERFORMANCE_METRICS.SLOW_MESSAGE_THRESHOLD) {
          this.onSlowMessage?.(record)
        }
      }
    }
  }

  /**
   * 获取当前性能指标
   */
  getMetrics(): PerformanceMetrics {
    const now = Date.now()
    const timeElapsed = (now - this.startTime) / 1000 // 秒
    const completedMessages = this.messageRecords.filter(r => r.latency !== undefined)
    const totalSize = this.messageRecords.reduce((sum, r) => sum + r.size, 0)

    // 计算延迟统计
    const sortedLatencies = [...this.latencyRecords].sort((a, b) => a - b)
    const p95Index = Math.floor(sortedLatencies.length * 0.95)
    const p99Index = Math.floor(sortedLatencies.length * 0.99)

    return {
      // 消息统计
      totalMessages: this.messageRecords.length,
      messagesPerSecond: timeElapsed > 0 ? this.messageRecords.length / timeElapsed : 0,
      averageMessageSize: this.messageRecords.length > 0 ? totalSize / this.messageRecords.length : 0,

      // 延迟统计
      averageLatency: this.latencyRecords.length > 0
        ? this.latencyRecords.reduce((sum, l) => sum + l, 0) / this.latencyRecords.length
        : 0,
      minLatency: sortedLatencies.length > 0 ? sortedLatencies[0] : 0,
      maxLatency: sortedLatencies.length > 0 ? sortedLatencies[sortedLatencies.length - 1] : 0,
      p95Latency: sortedLatencies.length > 0 ? sortedLatencies[p95Index] || 0 : 0,
      p99Latency: sortedLatencies.length > 0 ? sortedLatencies[p99Index] || 0 : 0,

      // 吞吐量统计
      bytesPerSecond: timeElapsed > 0 ? totalSize / timeElapsed : 0,
      messagesInQueue: this.messageRecords.filter(r => r.latency === undefined).length,

      // 错误统计
      errorRate: completedMessages.length > 0 ? this.errorCount / completedMessages.length : 0,
      timeoutRate: completedMessages.length > 0 ? this.timeoutCount / completedMessages.length : 0,

      // 内存使用
      memoryUsage: this.getCurrentMemoryUsage(),
      peakMemoryUsage: this.peakMemory,
    }
  }

  /**
   * 获取详细报告
   */
  getDetailedReport(): string {
    const metrics = this.getMetrics()

    return `
IPC Performance Report
=====================
Time Period: ${((Date.now() - this.startTime) / 1000).toFixed(2)}s

Message Statistics:
- Total Messages: ${metrics.totalMessages}
- Messages/sec: ${metrics.messagesPerSecond.toFixed(2)}
- Avg Message Size: ${(metrics.averageMessageSize / 1024).toFixed(2)} KB

Latency Statistics:
- Average: ${metrics.averageLatency.toFixed(2)}ms
- Min: ${metrics.minLatency}ms
- Max: ${metrics.maxLatency}ms
- P95: ${metrics.p95Latency.toFixed(2)}ms
- P99: ${metrics.p99Latency.toFixed(2)}ms

Throughput:
- Bytes/sec: ${(metrics.bytesPerSecond / 1024).toFixed(2)} KB/s
- Messages in Queue: ${metrics.messagesInQueue}

Error Rates:
- Error Rate: ${(metrics.errorRate * 100).toFixed(2)}%
- Timeout Rate: ${(metrics.timeoutRate * 100).toFixed(2)}%

Memory Usage:
- Current: ${(metrics.memoryUsage / 1024 / 1024).toFixed(2)} MB
- Peak: ${(metrics.peakMemoryUsage / 1024 / 1024).toFixed(2)} MB
    `.trim()
  }

  /**
   * 重置统计数据
   */
  reset(): void {
    this.messageRecords = []
    this.latencyRecords = []
    this.errorCount = 0
    this.timeoutCount = 0
    this.startTime = Date.now()
    this.peakMemory = 0
  }

  /**
   * 慢消息回调
   */
  onSlowMessage?: (record: MessageRecord) => void

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupOldRecords()
    }, this.cleanupInterval)
  }

  /**
   * 停止清理定时器
   */
  private stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
  }

  /**
   * 清理旧记录
   */
  private cleanupOldRecords(): void {
    if (this.messageRecords.length > this.maxRecords) {
      const removeCount = this.messageRecords.length - this.maxRecords
      this.messageRecords.splice(0, removeCount)
    }

    // 清理超过1小时的延迟记录
    const oneHourAgo = Date.now() - 3600000
    this.latencyRecords.splice(0, this.latencyRecords.length)

    // 重新收集最近的延迟数据
    this.latencyRecords = this.messageRecords
      .filter(r => r.timestamp > oneHourAgo && r.latency !== undefined)
      .map(r => r.latency!)
  }

  /**
   * 更新内存使用情况
   */
  private updateMemoryUsage(): void {
    const current = this.getCurrentMemoryUsage()
    if (current > this.peakMemory) {
      this.peakMemory = current
    }
  }

  /**
   * 获取当前内存使用量
   */
  private getCurrentMemoryUsage(): number {
    // 估算记录占用的内存
    const recordSize = 200 // 每条记录大约200字节
    return this.messageRecords.length * recordSize
  }

  /**
   * 销毁监控器
   */
  dispose(): void {
    this.disable()
    this.reset()
  }
}

// 默认性能监控器实例
export const defaultPerformanceMonitor = new PerformanceMonitor()
