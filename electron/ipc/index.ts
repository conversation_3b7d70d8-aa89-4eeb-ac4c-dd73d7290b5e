/**
 * IPC 库统一导出
 */

// 类型导出
export type {
  IPCMessage,
  RequestMessage,
  ResponseMessage,
  EventMessage,
  BatchMessage,
  MessageHandler,
  EventListener,
  IPCOptions,
  BaseMessage,
} from './types'

export {
  MessageType,
  MessagePriority,
  DEFAULT_IPC_OPTIONS,
  IPCError,
  IPCTimeoutError,
  IPCSerializationError,
  IPCChannelNotFoundError,
} from './types'

// 常量导出
export {
  IPC_CHANNELS,
  BUSINESS_CHANNELS,
  MESSAGE_ID_PREFIX,
  PERFORMANCE_METRICS,
  DEBUG_CONFIG,
  ERROR_CODES,
} from './constants'

export type {
  IPCChannel,
  BusinessChannel,
  ErrorCode,
} from './constants'

// 序列化工具导出
export {
  JSONSerializer,
  defaultSerializer,
  serialize,
  deserialize,
} from './serializer'

export type {
  SerializationOptions,
} from './serializer'

// 消息队列导出
export {
  MessageQueue,
  EventMerger,
} from './message-queue'

// 主进程 IPC 导出
export {
  MainIPC,
  mainIPC,
} from './main-ipc'

// 渲染进程 IPC 导出
export {
  RendererIPC,
} from './renderer-ipc'

// 性能监控导出
export {
  PerformanceMonitor,
  defaultPerformanceMonitor,
} from './performance-monitor'

export type {
  PerformanceMetrics,
} from './performance-monitor'

// 连接池导出
export {
  ConnectionPool,
  defaultConnectionPool,
  ConnectionStatus,
} from './connection-pool'

export type {
  ConnectionInfo,
  ConnectionPoolOptions,
} from './connection-pool'

// 便捷工具函数
export function createMainIPC(_options?: Partial<import('./types').IPCOptions>) {
  // 注意：这里需要在实际使用时导入 MainIPC
  // import { MainIPC } from './main-ipc'
  // return new MainIPC(options)
  throw new Error('Please import MainIPC directly and create instance manually')
}

export function createRendererIPC(_ipcRenderer: import('electron').IpcRenderer, _options?: Partial<import('./types').IPCOptions>) {
  // 注意：这里需要在实际使用时导入 RendererIPC
  // import { RendererIPC } from './renderer-ipc'
  // return new RendererIPC(ipcRenderer, options)
  throw new Error('Please import RendererIPC directly and create instance manually')
}

// 预定义的业务处理器示例（仅作为模板，实际使用时需要实现具体逻辑）
export const businessHandlers = {
  // 用户相关处理器
  user: {
    async login(credentials: { username: string, password: string }) {
      // 登录逻辑 - 实际项目中需要实现
      return { success: true, token: 'mock-token', user: { id: 1, username: credentials.username } }
    },

    async logout() {
      // 登出逻辑 - 实际项目中需要实现
      return { success: true }
    },

    async getInfo(userId: number) {
      // 获取用户信息 - 实际项目中需要实现
      return { id: userId, username: 'user', email: '<EMAIL>' }
    },
  },

  // 文件操作处理器（仅在主进程中使用）
  file: {
    async read(_filePath: string) {
      // 文件读取逻辑 - 实际项目中需要使用 fs 模块
      throw new Error('File operations should be implemented in main process')
    },

    async write(_data: { filePath: string, content: string }) {
      // 文件写入逻辑 - 实际项目中需要使用 fs 模块
      throw new Error('File operations should be implemented in main process')
    },

    async delete(_filePath: string) {
      // 文件删除逻辑 - 实际项目中需要使用 fs 模块
      throw new Error('File operations should be implemented in main process')
    },
  },

  // 系统通知处理器（仅在主进程中使用）
  notification: {
    async show(_options: { title: string, body: string, icon?: string }) {
      // 显示系统通知 - 实际项目中需要使用 Electron Notification API
      throw new Error('Notification should be implemented in main process')
    },

    async close(_notificationId: number) {
      // 关闭通知 - 实际项目中需要实现
      return { success: true }
    },
  },
}

// 工具函数：注册业务处理器
export function registerBusinessHandlers(ipc: import('./main-ipc').MainIPC | import('./renderer-ipc').RendererIPC) {
  // 注册用户相关处理器
  ipc.handle('user:login', businessHandlers.user.login)
  ipc.handle('user:logout', businessHandlers.user.logout)
  ipc.handle('user:info', businessHandlers.user.getInfo)

  // 注册文件操作处理器（仅在主进程中）
  if ('invoke' in ipc) {
    // 这是主进程 IPC
    ipc.handle('file:read', businessHandlers.file.read)
    ipc.handle('file:write', businessHandlers.file.write)
    ipc.handle('file:delete', businessHandlers.file.delete)
    ipc.handle('notification:show', businessHandlers.notification.show)
    ipc.handle('notification:close', businessHandlers.notification.close)
  }
}

// 工具函数：创建类型安全的 IPC 客户端
export function createTypedIPC<T extends Record<string, any>>() {
  return {
    invoke: <K extends keyof T>(
      ipc: import('./renderer-ipc').RendererIPC,
      channel: K,
      data: Parameters<T[K]>[0],
    ): Promise<ReturnType<T[K]>> => {
      return ipc.invoke(channel as string, data)
    },

    handle: <K extends keyof T>(
      ipc: import('./main-ipc').MainIPC | import('./renderer-ipc').RendererIPC,
      channel: K,
      handler: T[K],
    ) => {
      ipc.handle(channel as string, handler)
    },

    emit: <K extends keyof T>(
      ipc: import('./main-ipc').MainIPC | import('./renderer-ipc').RendererIPC,
      channel: K,
      data: Parameters<T[K]>[0],
    ) => {
      if ('emit' in ipc) {
        ipc.emit(channel as string, data)
      }
    },

    on: <K extends keyof T>(
      ipc: import('./main-ipc').MainIPC | import('./renderer-ipc').RendererIPC,
      channel: K,
      listener: T[K],
    ) => {
      ipc.on(channel as string, listener)
    },
  }
}

// 预定义的类型安全接口示例
export interface BusinessIPCInterface {
  'user:login': (credentials: { username: string, password: string }) => Promise<{ success: boolean, token: string, user: any }>
  'user:logout': () => Promise<{ success: boolean }>
  'user:info': (userId: number) => Promise<{ id: number, username: string, email: string }>
  'file:read': (filePath: string) => Promise<string>
  'file:write': (filePath: string, content: string) => Promise<{ success: boolean }>
  'file:delete': (filePath: string) => Promise<{ success: boolean }>
  'notification:show': (options: { title: string, body: string, icon?: string }) => Promise<{ success: boolean, id: number }>
  'notification:close': (notificationId: number) => Promise<{ success: boolean }>
  'app:state-change': (state: 'active' | 'inactive' | 'background') => void
  'app:theme-change': (theme: 'light' | 'dark' | 'auto') => void
  'app:language-change': (language: string) => void
}

// 创建类型安全的业务 IPC 客户端
export const businessIPC = createTypedIPC<BusinessIPCInterface>()

// 默认配置预设
export const presets = {
  // 高性能配置
  highPerformance: {
    batchSize: 20,
    batchTimeout: 8, // ~120fps
    defaultTimeout: 3000,
    maxRetries: 2,
    enableCompression: true,
    enableDebug: false,
  } as Partial<import('./types').IPCOptions>,

  // 开发调试配置
  development: {
    batchSize: 5,
    batchTimeout: 32, // ~30fps
    defaultTimeout: 10000,
    maxRetries: 5,
    enableCompression: false,
    enableDebug: true,
  } as Partial<import('./types').IPCOptions>,

  // 生产环境配置
  production: {
    batchSize: 15,
    batchTimeout: 16, // ~60fps
    defaultTimeout: 5000,
    maxRetries: 3,
    enableCompression: true,
    enableDebug: false,
  } as Partial<import('./types').IPCOptions>,
}
