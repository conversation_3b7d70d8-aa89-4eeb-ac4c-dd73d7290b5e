/**
 * IPC 消息序列化工具
 * 提供高效的 JSON 序列化和反序列化功能
 */

import { IPCSerializationError } from './types'

// 序列化选项
export interface SerializationOptions {
  enableCompression: boolean
  enableValidation: boolean
  maxSize: number // 最大消息大小（字节）
}

// 默认序列化选项
const DEFAULT_OPTIONS: SerializationOptions = {
  enableCompression: false,
  enableValidation: true,
  maxSize: 1024 * 1024, // 1MB
}

/**
 * JSON 序列化器类
 */
export class JSONSerializer {
  private options: SerializationOptions

  constructor(options: Partial<SerializationOptions> = {}) {
    this.options = { ...DEFAULT_OPTIONS, ...options }
  }

  /**
   * 序列化数据为 JSON 字符串
   */
  serialize<T = any>(data: T): string {
    try {
      // 预处理数据
      const processedData = this.preprocessData(data)

      // 序列化为 JSON
      let jsonString = JSON.stringify(processedData, this.replacer)

      // 验证大小
      if (this.options.enableValidation) {
        this.validateSize(jsonString)
      }

      // 压缩（如果启用）
      if (this.options.enableCompression) {
        jsonString = this.compress(jsonString)
      }

      return jsonString
    }
    catch (error) {
      throw new IPCSerializationError(error as Error)
    }
  }

  /**
   * 反序列化 JSON 字符串为数据
   */
  deserialize<T = any>(jsonString: string): T {
    try {
      // 解压缩（如果需要）
      let processedString = jsonString
      if (this.options.enableCompression && this.isCompressed(jsonString)) {
        processedString = this.decompress(jsonString)
      }

      // 验证大小
      if (this.options.enableValidation) {
        this.validateSize(processedString)
      }

      // 反序列化
      const data = JSON.parse(processedString, this.reviver)

      // 后处理数据
      return this.postprocessData(data)
    }
    catch (error) {
      throw new IPCSerializationError(error as Error)
    }
  }

  /**
   * 数据预处理
   * 处理特殊类型（Date、RegExp、Function 等）
   */
  private preprocessData(data: any): any {
    if (data === null || data === undefined) {
      return data
    }

    if (data instanceof Date) {
      return { __type: 'Date', value: data.toISOString() }
    }

    if (data instanceof RegExp) {
      return { __type: 'RegExp', source: data.source, flags: data.flags }
    }

    if (data instanceof Error) {
      return {
        __type: 'Error',
        name: data.name,
        message: data.message,
        stack: data.stack,
      }
    }

    if (data instanceof Map) {
      return { __type: 'Map', entries: Array.from(data.entries()) }
    }

    if (data instanceof Set) {
      return { __type: 'Set', values: Array.from(data.values()) }
    }

    if (ArrayBuffer.isView(data)) {
      return {
        __type: 'TypedArray',
        constructor: data.constructor.name,
        data: Array.from(data as any),
      }
    }

    if (data instanceof ArrayBuffer) {
      return {
        __type: 'ArrayBuffer',
        data: Array.from(new Uint8Array(data)),
      }
    }

    if (Array.isArray(data)) {
      return data.map(item => this.preprocessData(item))
    }

    if (typeof data === 'object') {
      const result: any = {}
      for (const [key, value] of Object.entries(data)) {
        result[key] = this.preprocessData(value)
      }
      return result
    }

    return data
  }

  /**
   * 数据后处理
   * 恢复特殊类型
   */
  private postprocessData(data: any): any {
    if (data === null || data === undefined) {
      return data
    }

    if (typeof data === 'object' && data.__type) {
      switch (data.__type) {
        case 'Date':
          return new Date(data.value)

        case 'RegExp':
          return new RegExp(data.source, data.flags)

        case 'Error': {
          const error = new Error(data.message)
          error.name = data.name
          error.stack = data.stack
          return error
        }

        case 'Map':
          return new Map(data.entries.map(([k, v]: [any, any]) => [
            this.postprocessData(k),
            this.postprocessData(v),
          ]))

        case 'Set':
          return new Set(data.values.map((v: any) => this.postprocessData(v)))

        case 'TypedArray': {
          const TypedArrayConstructor = (globalThis as any)[data.constructor]
          return new TypedArrayConstructor(data.data)
        }

        case 'ArrayBuffer':
          return new Uint8Array(data.data).buffer
      }
    }

    if (Array.isArray(data)) {
      return data.map(item => this.postprocessData(item))
    }

    if (typeof data === 'object') {
      const result: any = {}
      for (const [key, value] of Object.entries(data)) {
        result[key] = this.postprocessData(value)
      }
      return result
    }

    return data
  }

  /**
   * JSON.stringify 替换器
   */
  private replacer = (_key: string, value: any): any => {
    // 过滤函数类型
    if (typeof value === 'function') {
      return undefined
    }

    // 过滤 Symbol 类型
    if (typeof value === 'symbol') {
      return undefined
    }

    return value
  }

  /**
   * JSON.parse 恢复器
   */
  private reviver = (_key: string, value: any): any => {
    return value
  }

  /**
   * 验证消息大小
   */
  private validateSize(jsonString: string): void {
    const size = new Blob([jsonString]).size
    if (size > this.options.maxSize) {
      throw new Error(`Message size ${size} exceeds maximum allowed size ${this.options.maxSize}`)
    }
  }

  /**
   * 简单压缩（使用基础字符串压缩）
   */
  private compress(data: string): string {
    try {
      // 使用简单的重复字符压缩
      const compressed = this.simpleCompress(data)
      return `__compressed__${compressed}`
    }
    catch {
      // 如果压缩失败，返回原始数据
      return data
    }
  }

  /**
   * 解压缩
   */
  private decompress(data: string): string {
    try {
      if (this.isCompressed(data)) {
        const compressed = data.replace('__compressed__', '')
        return this.simpleDecompress(compressed)
      }
      return data
    }
    catch {
      // 如果解压缩失败，返回原始数据
      return data
    }
  }

  /**
   * 简单字符串压缩
   */
  private simpleCompress(data: string): string {
    // 使用 base64 编码作为简单的"压缩"
    // 在实际应用中，这里可以使用更复杂的压缩算法
    return btoa(encodeURIComponent(data))
  }

  /**
   * 简单字符串解压缩
   */
  private simpleDecompress(compressed: string): string {
    return decodeURIComponent(atob(compressed))
  }

  /**
   * 检查是否为压缩数据
   */
  private isCompressed(data: string): boolean {
    return data.startsWith('__compressed__')
  }
}

// 默认序列化器实例
export const defaultSerializer = new JSONSerializer()

// 便捷函数
export const serialize = <T = any>(data: T): string => defaultSerializer.serialize(data)
export const deserialize = <T = any>(jsonString: string): T => defaultSerializer.deserialize<T>(jsonString)
