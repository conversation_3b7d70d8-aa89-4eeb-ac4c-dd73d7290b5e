/**
 * IPC 通信常量定义
 */

// 内部 IPC 通道名称
export const IPC_CHANNELS = {
  // 核心通信通道
  CORE_REQUEST: '__ipc_core_request__',
  CORE_RESPONSE: '__ipc_core_response__',
  CORE_EVENT: '__ipc_core_event__',
  CORE_BATCH: '__ipc_core_batch__',

  // 渲染进程间通信通道
  RENDERER_BRIDGE: '__ipc_renderer_bridge__',
  RENDERER_REGISTER: '__ipc_renderer_register__',
  RENDERER_UNREGISTER: '__ipc_renderer_unregister__',
} as const

// 预定义的业务通道
export const BUSINESS_CHANNELS = {
  // 用户相关
  USER_LOGIN: 'user:login',
  USER_LOGOUT: 'user:logout',
  USER_INFO: 'user:info',

  // 文件操作
  FILE_READ: 'file:read',
  FILE_WRITE: 'file:write',
  FILE_DELETE: 'file:delete',
  FILE_UPLOAD: 'file:upload',

} as const

// 消息 ID 前缀
export const MESSAGE_ID_PREFIX = {
  REQUEST: 'req_',
  RESPONSE: 'res_',
  EVENT: 'evt_',
  BATCH: 'bat_',
} as const

// 性能监控常量
export const PERFORMANCE_METRICS = {
  // 消息处理时间阈值（毫秒）
  SLOW_MESSAGE_THRESHOLD: 100,

  // 队列大小警告阈值
  QUEUE_SIZE_WARNING: 100,
  QUEUE_SIZE_ERROR: 500,

  // 内存使用警告阈值（MB）
  MEMORY_WARNING_THRESHOLD: 50,
  MEMORY_ERROR_THRESHOLD: 100,
} as const

// 调试相关常量
export const DEBUG_CONFIG = {
  // 日志级别
  LOG_LEVELS: {
    ERROR: 0,
    WARN: 1,
    INFO: 2,
    DEBUG: 3,
    TRACE: 4,
  } as const,

  // 日志颜色
  LOG_COLORS: {
    ERROR: '\x1B[31m', // 红色
    WARN: '\x1B[33m', // 黄色
    INFO: '\x1B[36m', // 青色
    DEBUG: '\x1B[32m', // 绿色
    TRACE: '\x1B[37m', // 白色
    RESET: '\x1B[0m', // 重置
  } as const,
} as const

// 错误代码
export const ERROR_CODES = {
  // 通用错误
  UNKNOWN: 'UNKNOWN',
  TIMEOUT: 'TIMEOUT',
  CANCELLED: 'CANCELLED',

  // 序列化错误
  SERIALIZATION_FAILED: 'SERIALIZATION_FAILED',
  DESERIALIZATION_FAILED: 'DESERIALIZATION_FAILED',

  // 通道错误
  CHANNEL_NOT_FOUND: 'CHANNEL_NOT_FOUND',
  CHANNEL_ALREADY_EXISTS: 'CHANNEL_ALREADY_EXISTS',

  // 权限错误
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  UNAUTHORIZED: 'UNAUTHORIZED',

  // 网络错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  CONNECTION_LOST: 'CONNECTION_LOST',
} as const

// 类型导出
export type IPCChannel = typeof IPC_CHANNELS[keyof typeof IPC_CHANNELS]
export type BusinessChannel = typeof BUSINESS_CHANNELS[keyof typeof BUSINESS_CHANNELS]
export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]
