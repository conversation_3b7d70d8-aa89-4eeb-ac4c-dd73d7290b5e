/**
 * 主进程 IPC 处理器
 */

import { ipcMain, webContents } from 'electron'
import type {
  BatchMessage,
  EventListener,
  EventMessage,
  IPCOptions,
  MessageHandler,
  RequestMessage,
  ResponseMessage,
} from './types'
import {
  DEFAULT_IPC_OPTIONS,
  IPCError,
  IPCTimeoutError,
  MessagePriority,
  MessageType,
} from './types'
import { IPC_CHANNELS, MESSAGE_ID_PREFIX } from './constants'
import { deserialize, serialize } from './serializer'
import { EventMerger, MessageQueue } from './message-queue'
import { type PerformanceMonitor, defaultPerformanceMonitor } from './performance-monitor'
import type { ConnectionPool } from './connection-pool'
import { defaultConnectionPool } from './connection-pool'

/**
 * 主进程 IPC 管理器
 */
export class MainIPC {
  private handlers: Map<string, MessageHandler> = new Map()
  private eventListeners: Map<string, Set<EventListener>> = new Map()
  private pendingRequests: Map<string, {
    resolve: (value: any) => void
    reject: (error: Error) => void
    timeout: NodeJS.Timeout
  }> = new Map()

  private messageQueue: MessageQueue
  private eventMerger: EventMerger
  private performanceMonitor: PerformanceMonitor
  private connectionPool: ConnectionPool
  private options: IPCOptions
  private isInitialized = false

  // 渲染进程注册表
  private rendererRegistry: Map<number, {
    webContentsId: number
    channels: Set<string>
    lastSeen: number
  }> = new Map()

  constructor(options: Partial<IPCOptions> = {}) {
    this.options = { ...DEFAULT_IPC_OPTIONS, ...options }
    this.messageQueue = new MessageQueue(this.options)
    this.eventMerger = new EventMerger(this.options.batchTimeout)
    this.performanceMonitor = defaultPerformanceMonitor
    this.connectionPool = defaultConnectionPool

    // 启用性能监控（如果在调试模式下）
    if (this.options.enableDebug) {
      this.performanceMonitor.enable()
    }

    this.setupMessageQueue()
    this.setupEventMerger()
    this.setupPerformanceMonitoring()
  }

  /**
   * 初始化主进程 IPC
   */
  initialize(): void {
    if (this.isInitialized) {
      return
    }

    this.setupIPCHandlers()
    this.setupRendererManagement()
    this.isInitialized = true

    if (this.options.enableDebug) {
      // eslint-disable-next-line no-console
      console.log('[MainIPC] Initialized')
    }
  }

  /**
   * 注册消息处理器
   */
  handle<T = any>(channel: string, handler: MessageHandler<T>): void {
    if (this.handlers.has(channel)) {
      console.warn(`[MainIPC] Handler for channel '${channel}' already exists, overriding`)
    }

    this.handlers.set(channel, handler)

    if (this.options.enableDebug) {
      // eslint-disable-next-line no-console
      console.log(`[MainIPC] Registered handler for channel: ${channel}`)
    }
  }

  /**
   * 取消注册消息处理器
   */
  unhandle(channel: string): void {
    this.handlers.delete(channel)

    if (this.options.enableDebug) {
      // eslint-disable-next-line no-console
      console.log(`[MainIPC] Unregistered handler for channel: ${channel}`)
    }
  }

  /**
   * 监听事件
   */
  on<T = any>(channel: string, listener: EventListener<T>): void {
    if (!this.eventListeners.has(channel)) {
      this.eventListeners.set(channel, new Set())
    }

    this.eventListeners.get(channel)!.add(listener)

    if (this.options.enableDebug) {
      // eslint-disable-next-line no-console
      console.log(`[MainIPC] Added event listener for channel: ${channel}`)
    }
  }

  /**
   * 移除事件监听器
   */
  off<T = any>(channel: string, listener?: EventListener<T>): void {
    const listeners = this.eventListeners.get(channel)
    if (!listeners)
      return

    if (listener) {
      listeners.delete(listener)
      if (listeners.size === 0) {
        this.eventListeners.delete(channel)
      }
    }
    else {
      this.eventListeners.delete(channel)
    }

    if (this.options.enableDebug) {
      // eslint-disable-next-line no-console
      console.log(`[MainIPC] Removed event listener for channel: ${channel}`)
    }
  }

  /**
   * 发送请求到渲染进程
   */
  async invoke<T = any, R = any>(
    webContentsId: number, // 目标渲染进程的ID
    channel: string, // 通道名称，用于标识要调用的处理器
    data: T, // 发送的数据
    timeout?: number, // 发送的数据
  ): Promise<R> {
    const webContent = webContents.fromId(webContentsId)
    if (!webContent) {
      throw new IPCError(`WebContents with id ${webContentsId} not found`, 'WEBCONTENTS_NOT_FOUND')
    }

    const requestId = this.generateRequestId()
    const requestMessage: RequestMessage = {
      id: requestId,
      type: MessageType.REQUEST,
      channel,
      timestamp: Date.now(),
      priority: MessagePriority.NORMAL,
      data: serialize(data),
      timeout: timeout || this.options.defaultTimeout,
    }

    return new Promise<R>((resolve, reject) => {
      // 设置超时
      const timeoutHandle = setTimeout(() => {
        this.pendingRequests.delete(requestId)
        reject(new IPCTimeoutError(requestMessage.timeout!))
      }, requestMessage.timeout)

      // 保存请求信息
      this.pendingRequests.set(requestId, {
        resolve: (value: any) => {
          clearTimeout(timeoutHandle)
          resolve(value)
        },
        reject: (error: Error) => {
          clearTimeout(timeoutHandle)
          reject(error)
        },
        timeout: timeoutHandle,
      })

      // 发送请求
      webContent.send(IPC_CHANNELS.CORE_REQUEST, serialize(requestMessage))
    })
  }

  /**
   * 发送事件到渲染进程
   */
  emit<T = any>(channel: string, data: T, targetWebContentsId?: number): void {
    const eventMessage: EventMessage = {
      id: this.generateEventId(),
      type: MessageType.EVENT,
      channel,
      timestamp: Date.now(),
      priority: MessagePriority.NORMAL,
      data: serialize(data),
    }

    if (targetWebContentsId) {
      // 发送到指定渲染进程
      const webContent = webContents.fromId(targetWebContentsId)
      if (webContent) {
        this.eventMerger.addEvent(eventMessage, (events) => {
          webContent.send(IPC_CHANNELS.CORE_EVENT, serialize(events))
        })
      }
    }
    else {
      // 广播到所有渲染进程
      this.broadcastEvent(eventMessage)
    }
  }

  /**
   * 广播事件到所有渲染进程
   */
  private broadcastEvent(eventMessage: EventMessage): void {
    const allWebContents = webContents.getAllWebContents()

    this.eventMerger.addEvent(eventMessage, (events) => {
      const serializedEvents = serialize(events)
      allWebContents.forEach((webContent) => {
        if (!webContent.isDestroyed()) {
          webContent.send(IPC_CHANNELS.CORE_EVENT, serializedEvents)
        }
      })
    })
  }

  /**
   * 设置 IPC 处理器
   */
  private setupIPCHandlers(): void {
    // 处理来自渲染进程的请求
    ipcMain.on(IPC_CHANNELS.CORE_REQUEST, async (event, serializedMessage: string) => {
      try {
        const message = deserialize<RequestMessage>(serializedMessage)
        await this.handleRequest(event, message)
      }
      catch (error) {
        console.error('[MainIPC] Failed to handle request:', error)
      }
    })

    // 处理来自渲染进程的响应
    ipcMain.on(IPC_CHANNELS.CORE_RESPONSE, (_event, serializedMessage: string) => {
      try {
        const message = deserialize<ResponseMessage>(serializedMessage)
        this.handleResponse(message)
      }
      catch (error) {
        console.error('[MainIPC] Failed to handle response:', error)
      }
    })

    // 处理来自渲染进程的事件
    ipcMain.on(IPC_CHANNELS.CORE_EVENT, (_event, serializedMessage: string) => {
      try {
        const message = deserialize<EventMessage>(serializedMessage)
        this.handleEvent(message)
      }
      catch (error) {
        console.error('[MainIPC] Failed to handle event:', error)
      }
    })

    // 处理批量消息
    ipcMain.on(IPC_CHANNELS.CORE_BATCH, (event, serializedMessage: string) => {
      try {
        const batchMessage = deserialize<BatchMessage>(serializedMessage)
        this.handleBatch(event, batchMessage)
      }
      catch (error) {
        console.error('[MainIPC] Failed to handle batch:', error)
      }
    })
  }

  /**
   * 处理请求消息
   */
  private async handleRequest(event: Electron.IpcMainEvent, message: RequestMessage): Promise<void> {
    const handler = this.handlers.get(message.channel)
    if (!handler) {
      const errorResponse: ResponseMessage = {
        id: this.generateResponseId(),
        type: MessageType.RESPONSE,
        channel: message.channel,
        timestamp: Date.now(),
        priority: message.priority,
        requestId: message.id,
        success: false,
        error: `Handler not found for channel: ${message.channel}`,
      }

      event.reply(IPC_CHANNELS.CORE_RESPONSE, serialize(errorResponse))
      return
    }

    try {
      const requestData = deserialize(message.data)
      const result = await handler(requestData, message)

      const successResponse: ResponseMessage = {
        id: this.generateResponseId(),
        type: MessageType.RESPONSE,
        channel: message.channel,
        timestamp: Date.now(),
        priority: message.priority,
        requestId: message.id,
        success: true,
        data: serialize(result),
      }

      event.reply(IPC_CHANNELS.CORE_RESPONSE, serialize(successResponse))
    }
    catch (error) {
      const errorResponse: ResponseMessage = {
        id: this.generateResponseId(),
        type: MessageType.RESPONSE,
        channel: message.channel,
        timestamp: Date.now(),
        priority: message.priority,
        requestId: message.id,
        success: false,
        error: error instanceof Error ? error.message : String(error),
      }

      event.reply(IPC_CHANNELS.CORE_RESPONSE, serialize(errorResponse))
    }
  }

  /**
   * 处理响应消息
   */
  private handleResponse(message: ResponseMessage): void {
    const pendingRequest = this.pendingRequests.get(message.requestId)
    if (!pendingRequest) {
      if (this.options.enableDebug) {
        console.warn(`[MainIPC] No pending request found for response: ${message.requestId}`)
      }
      return
    }

    this.pendingRequests.delete(message.requestId)

    if (message.success) {
      const responseData = message.data ? deserialize(message.data) : undefined
      pendingRequest.resolve(responseData)
    }
    else {
      pendingRequest.reject(new IPCError(message.error || 'Unknown error', 'RESPONSE_ERROR'))
    }
  }

  /**
   * 处理事件消息
   */
  private handleEvent(message: EventMessage): void {
    const listeners = this.eventListeners.get(message.channel)
    if (!listeners || listeners.size === 0) {
      return
    }

    const eventData = deserialize(message.data)
    listeners.forEach((listener) => {
      try {
        listener(eventData, message)
      }
      catch (error) {
        console.error(`[MainIPC] Event listener error for channel '${message.channel}':`, error)
      }
    })
  }

  /**
   * 处理批量消息
   */
  private async handleBatch(event: Electron.IpcMainEvent, batchMessage: BatchMessage): Promise<void> {
    for (const message of batchMessage.messages) {
      switch (message.type) {
        case MessageType.REQUEST:
          await this.handleRequest(event, message as RequestMessage)
          break
        case MessageType.EVENT:
          this.handleEvent(message as EventMessage)
          break
        case MessageType.RESPONSE:
          this.handleResponse(message as ResponseMessage)
          break
      }
    }
  }

  /**
   * 设置消息队列
   */
  private setupMessageQueue(): void {
    this.messageQueue.onBatch((batchMessage) => {
      // 发送批量消息到所有渲染进程
      const allWebContents = webContents.getAllWebContents()
      const serializedBatch = serialize(batchMessage)

      allWebContents.forEach((webContent) => {
        if (!webContent.isDestroyed()) {
          webContent.send(IPC_CHANNELS.CORE_BATCH, serializedBatch)
        }
      })
    })
  }

  /**
   * 设置事件合并器
   */
  private setupEventMerger(): void {
    // 事件合并器的回调已在 emit 方法中设置
  }

  /**
   * 设置性能监控
   */
  private setupPerformanceMonitoring(): void {
    // 监控慢消息
    this.performanceMonitor.onSlowMessage = (record) => {
      if (this.options.enableDebug) {
        console.warn(`[MainIPC] Slow message detected: ${record.channel} took ${record.latency}ms`)
      }
    }

    // 设置连接池事件监听
    this.connectionPool.on('connection:error', (data) => {
      if (this.options.enableDebug) {
        console.error(`[MainIPC] Connection error for ${data.webContentsId}: ${data.error}`)
      }
    })
  }

  /**
   * 设置渲染进程管理
   */
  private setupRendererManagement(): void {
    // 渲染进程注册
    ipcMain.on(IPC_CHANNELS.RENDERER_REGISTER, (event, channels: string[]) => {
      const webContentsId = event.sender.id
      this.rendererRegistry.set(webContentsId, {
        webContentsId,
        channels: new Set(channels),
        lastSeen: Date.now(),
      })

      if (this.options.enableDebug) {
        // eslint-disable-next-line no-console
        console.log(`[MainIPC] Renderer ${webContentsId} registered with channels:`, channels)
      }
    })

    // 渲染进程注销
    ipcMain.on(IPC_CHANNELS.RENDERER_UNREGISTER, (event) => {
      const webContentsId = event.sender.id
      this.rendererRegistry.delete(webContentsId)

      if (this.options.enableDebug) {
        // eslint-disable-next-line no-console
        console.log(`[MainIPC] Renderer ${webContentsId} unregistered`)
      }
    })
  }

  /**
   * 生成请求 ID
   */
  private generateRequestId(): string {
    return `${MESSAGE_ID_PREFIX.REQUEST}${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 生成响应 ID
   */
  private generateResponseId(): string {
    return `${MESSAGE_ID_PREFIX.RESPONSE}${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 生成事件 ID
   */
  private generateEventId(): string {
    return `${MESSAGE_ID_PREFIX.EVENT}${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics() {
    return this.performanceMonitor.getMetrics()
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): string {
    return this.performanceMonitor.getDetailedReport()
  }

  /**
   * 获取连接池统计
   */
  getConnectionStats() {
    return this.connectionPool.getStats()
  }

  /**
   * 获取消息队列统计
   */
  getQueueStats() {
    return this.messageQueue.getStats()
  }

  /**
   * 重置性能统计
   */
  resetPerformanceStats(): void {
    this.performanceMonitor.reset()
    this.messageQueue.resetStats()
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.handlers.clear()
    this.eventListeners.clear()
    this.pendingRequests.forEach(({ timeout }) => clearTimeout(timeout))
    this.pendingRequests.clear()
    this.messageQueue.clear()
    this.eventMerger.clear()
    this.rendererRegistry.clear()
    this.performanceMonitor.dispose()
    this.connectionPool.dispose()

    if (this.options.enableDebug) {
      // eslint-disable-next-line no-console
      console.log('[MainIPC] Disposed')
    }
  }
}

// 默认主进程 IPC 实例
export const mainIPC = new MainIPC()
