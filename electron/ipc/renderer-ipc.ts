/**
 * 渲染进程 IPC 处理器
 */

import type { Ipc<PERSON>enderer } from 'electron'
import type {
  BatchMessage,
  EventListener,
  EventMessage,
  IPCOptions,
  MessageHandler,
  RequestMessage,
  ResponseMessage,
} from './types'
import {
  DEFAULT_IPC_OPTIONS,
  IPCError,
  IPCTimeoutError,
  MessagePriority,
  MessageType,
} from './types'
import { IPC_CHANNELS, MESSAGE_ID_PREFIX } from './constants'
import { deserialize, serialize } from './serializer'
import { EventMerger, MessageQueue } from './message-queue'

/**
 * 渲染进程 IPC 管理器
 */
export class RendererIPC {
  private ipcRenderer: IpcRenderer
  private handlers: Map<string, MessageHandler> = new Map()
  private eventListeners: Map<string, Set<EventListener>> = new Map()
  private pendingRequests: Map<string, {
    resolve: (value: any) => void
    reject: (error: Error) => void
    timeout: NodeJS.Timeout
  }> = new Map()

  private messageQueue: MessageQueue
  private eventMerger: EventMerger
  private options: IPCOptions
  private isInitialized = false

  // 渲染进程间通信支持
  private rendererBridge: Map<string, Set<EventListener>> = new Map()

  constructor(ipcRenderer: IpcRenderer, options: Partial<IPCOptions> = {}) {
    this.ipcRenderer = ipcRenderer
    this.options = { ...DEFAULT_IPC_OPTIONS, ...options }
    this.messageQueue = new MessageQueue(this.options)
    this.eventMerger = new EventMerger(this.options.batchTimeout)

    this.setupMessageQueue()
    this.setupEventMerger()
  }

  /**
   * 初始化渲染进程 IPC
   */
  initialize(): void {
    if (this.isInitialized) {
      return
    }

    this.setupIPCHandlers()
    this.registerWithMain()
    this.isInitialized = true

    if (this.options.enableDebug) {
      // eslint-disable-next-line no-console
      console.log('[RendererIPC] Initialized')
    }
  }

  /**
   * 注册消息处理器
   */
  handle<T = any>(channel: string, handler: MessageHandler<T>): void {
    if (this.handlers.has(channel)) {
      console.warn(`[RendererIPC] Handler for channel '${channel}' already exists, overriding`)
    }

    this.handlers.set(channel, handler)

    if (this.options.enableDebug) {
      // eslint-disable-next-line no-console
      console.log(`[RendererIPC] Registered handler for channel: ${channel}`)
    }
  }

  /**
   * 取消注册消息处理器
   */
  unhandle(channel: string): void {
    this.handlers.delete(channel)

    if (this.options.enableDebug) {
      // eslint-disable-next-line no-console
      console.log(`[RendererIPC] Unregistered handler for channel: ${channel}`)
    }
  }

  /**
   * 监听事件
   */
  on<T = any>(channel: string, listener: EventListener<T>): void {
    if (!this.eventListeners.has(channel)) {
      this.eventListeners.set(channel, new Set())
    }

    this.eventListeners.get(channel)!.add(listener)

    if (this.options.enableDebug) {
      // eslint-disable-next-line no-console
      console.log(`[RendererIPC] Added event listener for channel: ${channel}`)
    }
  }

  /**
   * 移除事件监听器
   */
  off<T = any>(channel: string, listener?: EventListener<T>): void {
    const listeners = this.eventListeners.get(channel)
    if (!listeners)
      return

    if (listener) {
      listeners.delete(listener)
      if (listeners.size === 0) {
        this.eventListeners.delete(channel)
      }
    }
    else {
      this.eventListeners.delete(channel)
    }

    if (this.options.enableDebug) {
      // eslint-disable-next-line no-console
      console.log(`[RendererIPC] Removed event listener for channel: ${channel}`)
    }
  }

  /**
   * 发送请求到主进程
   */
  async invoke<T = any, R = any>(
    channel: string,
    data: T,
    timeout?: number,
  ): Promise<R> {
    const requestId = this.generateRequestId()
    const requestMessage: RequestMessage = {
      id: requestId,
      type: MessageType.REQUEST,
      channel,
      timestamp: Date.now(),
      priority: MessagePriority.NORMAL,
      data: serialize(data),
      timeout: timeout || this.options.defaultTimeout,
    }

    return new Promise<R>((resolve, reject) => {
      // 设置超时
      const timeoutHandle = setTimeout(() => {
        this.pendingRequests.delete(requestId)
        reject(new IPCTimeoutError(requestMessage.timeout!))
      }, requestMessage.timeout)

      // 保存请求信息
      this.pendingRequests.set(requestId, {
        resolve: (value: any) => {
          clearTimeout(timeoutHandle)
          resolve(value)
        },
        reject: (error: Error) => {
          clearTimeout(timeoutHandle)
          reject(error)
        },
        timeout: timeoutHandle,
      })

      // 发送请求
      this.ipcRenderer.send(IPC_CHANNELS.CORE_REQUEST, serialize(requestMessage))
    })
  }

  /**
   * 发送事件到主进程
   */
  emit<T = any>(channel: string, data: T): void {
    const eventMessage: EventMessage = {
      id: this.generateEventId(),
      type: MessageType.EVENT,
      channel,
      timestamp: Date.now(),
      priority: MessagePriority.NORMAL,
      data: serialize(data),
    }

    this.eventMerger.addEvent(eventMessage, (events) => {
      this.ipcRenderer.send(IPC_CHANNELS.CORE_EVENT, serialize(events))
    })
  }

  /**
   * 发送事件到其他渲染进程
   */
  emitToRenderer<T = any>(channel: string, data: T): void {
    const eventMessage: EventMessage = {
      id: this.generateEventId(),
      type: MessageType.EVENT,
      channel,
      timestamp: Date.now(),
      priority: MessagePriority.NORMAL,
      data: serialize(data),
    }

    // 通过主进程转发到其他渲染进程
    this.ipcRenderer.send(IPC_CHANNELS.RENDERER_BRIDGE, serialize(eventMessage))
  }

  /**
   * 监听来自其他渲染进程的事件
   */
  onRenderer<T = any>(channel: string, listener: EventListener<T>): void {
    if (!this.rendererBridge.has(channel)) {
      this.rendererBridge.set(channel, new Set())
    }

    this.rendererBridge.get(channel)!.add(listener)

    if (this.options.enableDebug) {
      // eslint-disable-next-line no-console
      console.log(`[RendererIPC] Added renderer bridge listener for channel: ${channel}`)
    }
  }

  /**
   * 移除渲染进程间事件监听器
   */
  offRenderer<T = any>(channel: string, listener?: EventListener<T>): void {
    const listeners = this.rendererBridge.get(channel)
    if (!listeners)
      return

    if (listener) {
      listeners.delete(listener)
      if (listeners.size === 0) {
        this.rendererBridge.delete(channel)
      }
    }
    else {
      this.rendererBridge.delete(channel)
    }

    if (this.options.enableDebug) {
      // eslint-disable-next-line no-console
      console.log(`[RendererIPC] Removed renderer bridge listener for channel: ${channel}`)
    }
  }

  /**
   * 设置 IPC 处理器
   */
  private setupIPCHandlers(): void {
    // 处理来自主进程的请求
    this.ipcRenderer.on(IPC_CHANNELS.CORE_REQUEST, async (_event, serializedMessage: string) => {
      try {
        const message = deserialize<RequestMessage>(serializedMessage)
        await this.handleRequest(message)
      }
      catch (error) {
        console.error('[RendererIPC] Failed to handle request:', error)
      }
    })

    // 处理来自主进程的响应
    this.ipcRenderer.on(IPC_CHANNELS.CORE_RESPONSE, (_event, serializedMessage: string) => {
      try {
        const message = deserialize<ResponseMessage>(serializedMessage)
        this.handleResponse(message)
      }
      catch (error) {
        console.error('[RendererIPC] Failed to handle response:', error)
      }
    })

    // 处理来自主进程的事件
    this.ipcRenderer.on(IPC_CHANNELS.CORE_EVENT, (_event, serializedMessage: string) => {
      try {
        const events = deserialize<EventMessage[]>(serializedMessage)
        events.forEach(message => this.handleEvent(message))
      }
      catch (error) {
        console.error('[RendererIPC] Failed to handle event:', error)
      }
    })

    // 处理批量消息
    this.ipcRenderer.on(IPC_CHANNELS.CORE_BATCH, (_event, serializedMessage: string) => {
      try {
        const batchMessage = deserialize<BatchMessage>(serializedMessage)
        this.handleBatch(batchMessage)
      }
      catch (error) {
        console.error('[RendererIPC] Failed to handle batch:', error)
      }
    })

    // 处理渲染进程间通信
    this.ipcRenderer.on(IPC_CHANNELS.RENDERER_BRIDGE, (_event, serializedMessage: string) => {
      try {
        const message = deserialize<EventMessage>(serializedMessage)
        this.handleRendererBridge(message)
      }
      catch (error) {
        console.error('[RendererIPC] Failed to handle renderer bridge:', error)
      }
    })
  }

  /**
   * 处理请求消息
   */
  private async handleRequest(message: RequestMessage): Promise<void> {
    const handler = this.handlers.get(message.channel)
    if (!handler) {
      const errorResponse: ResponseMessage = {
        id: this.generateResponseId(),
        type: MessageType.RESPONSE,
        channel: message.channel,
        timestamp: Date.now(),
        priority: message.priority,
        requestId: message.id,
        success: false,
        error: `Handler not found for channel: ${message.channel}`,
      }

      this.ipcRenderer.send(IPC_CHANNELS.CORE_RESPONSE, serialize(errorResponse))
      return
    }

    try {
      const requestData = deserialize(message.data)
      const result = await handler(requestData, message)

      const successResponse: ResponseMessage = {
        id: this.generateResponseId(),
        type: MessageType.RESPONSE,
        channel: message.channel,
        timestamp: Date.now(),
        priority: message.priority,
        requestId: message.id,
        success: true,
        data: serialize(result),
      }

      this.ipcRenderer.send(IPC_CHANNELS.CORE_RESPONSE, serialize(successResponse))
    }
    catch (error) {
      const errorResponse: ResponseMessage = {
        id: this.generateResponseId(),
        type: MessageType.RESPONSE,
        channel: message.channel,
        timestamp: Date.now(),
        priority: message.priority,
        requestId: message.id,
        success: false,
        error: error instanceof Error ? error.message : String(error),
      }

      this.ipcRenderer.send(IPC_CHANNELS.CORE_RESPONSE, serialize(errorResponse))
    }
  }

  /**
   * 处理响应消息
   */
  private handleResponse(message: ResponseMessage): void {
    const pendingRequest = this.pendingRequests.get(message.requestId)
    if (!pendingRequest) {
      if (this.options.enableDebug) {
        console.warn(`[RendererIPC] No pending request found for response: ${message.requestId}`)
      }
      return
    }

    this.pendingRequests.delete(message.requestId)

    if (message.success) {
      const responseData = message.data ? deserialize(message.data) : undefined
      pendingRequest.resolve(responseData)
    }
    else {
      pendingRequest.reject(new IPCError(message.error || 'Unknown error', 'RESPONSE_ERROR'))
    }
  }

  /**
   * 处理事件消息
   */
  private handleEvent(message: EventMessage): void {
    const listeners = this.eventListeners.get(message.channel)
    if (!listeners || listeners.size === 0) {
      return
    }

    const eventData = deserialize(message.data)
    listeners.forEach((listener) => {
      try {
        listener(eventData, message)
      }
      catch (error) {
        console.error(`[RendererIPC] Event listener error for channel '${message.channel}':`, error)
      }
    })
  }

  /**
   * 处理渲染进程间通信
   */
  private handleRendererBridge(message: EventMessage): void {
    const listeners = this.rendererBridge.get(message.channel)
    if (!listeners || listeners.size === 0) {
      return
    }

    const eventData = deserialize(message.data)
    listeners.forEach((listener) => {
      try {
        listener(eventData, message)
      }
      catch (error) {
        console.error(`[RendererIPC] Renderer bridge listener error for channel '${message.channel}':`, error)
      }
    })
  }

  /**
   * 处理批量消息
   */
  private async handleBatch(batchMessage: BatchMessage): Promise<void> {
    for (const message of batchMessage.messages) {
      switch (message.type) {
        case MessageType.REQUEST:
          await this.handleRequest(message as RequestMessage)
          break
        case MessageType.EVENT:
          this.handleEvent(message as EventMessage)
          break
        case MessageType.RESPONSE:
          this.handleResponse(message as ResponseMessage)
          break
      }
    }
  }

  /**
   * 向主进程注册当前渲染进程
   */
  private registerWithMain(): void {
    const channels = Array.from(this.handlers.keys())
    this.ipcRenderer.send(IPC_CHANNELS.RENDERER_REGISTER, channels)
  }

  /**
   * 设置消息队列
   */
  private setupMessageQueue(): void {
    this.messageQueue.onBatch((batchMessage) => {
      this.ipcRenderer.send(IPC_CHANNELS.CORE_BATCH, serialize(batchMessage))
    })
  }

  /**
   * 设置事件合并器
   */
  private setupEventMerger(): void {
    // 事件合并器的回调已在 emit 方法中设置
  }

  /**
   * 生成请求 ID
   */
  private generateRequestId(): string {
    return `${MESSAGE_ID_PREFIX.REQUEST}${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 生成响应 ID
   */
  private generateResponseId(): string {
    return `${MESSAGE_ID_PREFIX.RESPONSE}${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 生成事件 ID
   */
  private generateEventId(): string {
    return `${MESSAGE_ID_PREFIX.EVENT}${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 清理资源
   */
  dispose(): void {
    // 向主进程注销
    this.ipcRenderer.send(IPC_CHANNELS.RENDERER_UNREGISTER)

    // 清理资源
    this.handlers.clear()
    this.eventListeners.clear()
    this.rendererBridge.clear()
    this.pendingRequests.forEach(({ timeout }) => clearTimeout(timeout))
    this.pendingRequests.clear()
    this.messageQueue.clear()
    this.eventMerger.clear()

    // 移除所有 IPC 监听器
    Object.values(IPC_CHANNELS).forEach((channel) => {
      this.ipcRenderer.removeAllListeners(channel)
    })

    if (this.options.enableDebug) {
      // eslint-disable-next-line no-console
      console.log('[RendererIPC] Disposed')
    }
  }
}
