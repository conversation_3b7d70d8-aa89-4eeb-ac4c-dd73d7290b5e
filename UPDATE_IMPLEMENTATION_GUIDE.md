# Electron 更新功能实现指南

## 🎉 实现完成

基于 electron-updater 的版本更新功能已经完全实现，支持全量和增量更新，macOS 上只使用全量更新。

## ✅ 已实现的功能

### 1. 核心更新服务
- **UpdateService** - 主进程更新服务，处理更新逻辑
- **UpdateHandler** - IPC 处理器，处理渲染进程的更新请求
- **electron-updater 配置** - 支持不同平台的更新策略

### 2. 用户界面
- **UpdateDialog** - 更新对话框组件，显示更新进度和状态
- **更新管理页面** - 完整的更新管理界面
- **更新策略页面** - 更新策略说明和配置文档

### 3. 状态管理
- **useUpdate** - Vue Composable，管理更新状态和操作
- **全局更新监听** - 在 App.vue 中集成全局更新对话框

### 4. 平台支持
- **macOS** - 全量更新（DMG/ZIP）
- **Windows** - 增量更新（NSIS 差分包）
- **Linux** - 增量更新（AppImage/DEB/RPM 差分包）

## 📁 文件结构

```
electron/
├── main/
│   ├── services/
│   │   └── update-service.ts          # 更新服务
│   ├── ipc/
│   │   └── update-handler.ts          # IPC 处理器
│   └── core/
│       └── bootstrap.ts               # 集成更新服务

src/
├── components/
│   └── common/
│       └── UpdateDialog.vue          # 更新对话框
├── composables/
│   └── use-update.ts                 # 更新状态管理
├── utils/
│   └── electron-update.ts            # 更新工具函数
├── views/
│   └── system/
│       ├── update/
│       │   └── index.vue             # 更新管理页面
│       └── update-strategy/
│           └── index.vue             # 更新策略页面
└── App.vue                           # 集成全局更新对话框

electron-builder.json                 # 构建配置
```

## 🔧 配置说明

### electron-builder.json 配置

```json
{
  "publish": {
    "provider": "generic",
    "url": "https://example.com/auto-updates",
    "channel": "latest"
  },
  "nsis": {
    "differentialPackage": true // Windows 增量更新
  },
  "generateUpdatesFilesForAllChannels": true
}
```

### 更新策略

- **macOS**: 只使用全量更新，每次下载完整应用包
- **Windows**: 支持增量更新，使用 NSIS 差分包
- **Linux**: 支持增量更新，使用各种格式的差分包

## 🚀 使用方法

### 1. 在应用中使用

```typescript
import { useUpdate } from '@/composables/use-update'

const {
  updateInfo,
  currentVersion,
  isChecking,
  isDownloading,
  checkForUpdates,
  downloadUpdate,
  installUpdate
} = useUpdate()

// 检查更新
await checkForUpdates()

// 下载更新
await downloadUpdate()

// 安装更新
await installUpdate()
```

### 2. 监听更新状态

```typescript
import { electronUpdateAPI } from '@/utils/electron-update'

// 监听更新状态变化
const removeListener = electronUpdateAPI.onUpdateStatus((info) => {
  console.log('更新状态:', info.status)
  console.log('更新进度:', info.progress)
})

// 清理监听器
removeListener()
```

### 3. 手动操作

```typescript
// 设置更新服务器
await electronUpdateAPI.setUpdateServerUrl('https://your-server.com/updates')

// 获取当前版本
const version = await electronUpdateAPI.getCurrentVersion()

// 获取更新信息
const updateInfo = await electronUpdateAPI.getUpdateInfo()
```

## 🌐 更新服务器要求

### 元数据文件格式

更新服务器需要提供以下格式的 YAML 文件：

#### macOS (latest-mac.yml)
```yaml
version: 1.6.0
files:
  - url: soybean-admin-1.6.0-mac.zip
    sha512: abc123def456...
    size: 100000000
path: soybean-admin-1.6.0-mac.zip
sha512: abc123def456...
releaseDate: 2024-01-01T00:00:00.000Z
releaseNotes: 新版本包含重要更新和修复
```

#### Windows (latest.yml)
```yaml
version: 1.6.0
files:
  - url: soybean-admin-1.6.0.exe
    sha512: def456ghi789...
    size: 120000000
path: soybean-admin-1.6.0.exe
sha512: def456ghi789...
releaseDate: 2024-01-01T00:00:00.000Z
releaseNotes: 新版本包含重要更新和修复
```

#### Linux (latest-linux.yml)
```yaml
version: 1.6.0
files:
  - url: soybean-admin-1.6.0.AppImage
    sha512: ghi789jkl012...
    size: 110000000
path: soybean-admin-1.6.0.AppImage
sha512: ghi789jkl012...
releaseDate: 2024-01-01T00:00:00.000Z
releaseNotes: 新版本包含重要更新和修复
```

### 服务器端点

- `GET /updates/latest-mac.yml` - macOS 更新信息
- `GET /updates/latest.yml` - Windows 更新信息
- `GET /updates/latest-linux.yml` - Linux 更新信息
- `GET /updates/{filename}` - 更新文件下载

## 🧪 测试方法

### 1. 开发环境测试

```bash
# 启动应用
pnpm dev

# 访问更新管理页面
# 点击"检查更新"按钮测试功能
```

### 2. 生产环境测试

```bash
# 构建应用
pnpm build-electron

# 配置真实的更新服务器 URL
# 发布应用并测试更新流程
```

## 📝 注意事项

1. **开发环境**: 更新功能主要用于测试 UI 和逻辑
2. **生产环境**: 需要配置真实的更新服务器
3. **安全性**: 更新文件需要 SHA512 校验
4. **网络**: 建议在良好的网络环境下进行更新
5. **备份**: 更新前会自动处理数据备份

## 🔗 相关链接

- [electron-updater 文档](https://www.electron.build/auto-update)
- [electron-builder 配置](https://www.electron.build/configuration/configuration)
- [更新服务器搭建指南](https://www.electron.build/tutorials/release-using-channels)

## 🎯 下一步

1. 配置真实的更新服务器
2. 设置 CI/CD 自动发布流程
3. 添加更新通知和用户偏好设置
4. 实现更新回滚功能
5. 添加更新统计和监控

现在您的 Electron 应用已经具备完整的更新功能！🎉
