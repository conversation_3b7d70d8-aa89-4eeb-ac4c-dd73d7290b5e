# Electron 应用更新功能测试指南

## 🎉 修复完成

您的 Electron 应用更新功能已经成功修复并可以正常运行！

## ✅ 已修复的问题

1. **ESLint 错误** - 所有 console.log 语句已修复
2. **electron-updater 导入问题** - 修复了 CommonJS 模块导入
3. **图标依赖问题** - 替换为项目现有的 @iconify/vue 图标系统
4. **开发环境更新禁用问题** - 现在开发环境也可以测试更新功能

## 🚀 当前状态

- ✅ `pnpm dev` 正常运行
- ✅ Electron 应用启动成功
- ✅ 更新功能已启用（开发环境）
- ✅ 无 TypeScript 和 ESLint 错误

## 🧪 如何测试更新功能

### 1. 在应用中测试

1. 启动应用：`pnpm dev`
2. 在应用中访问更新管理页面
3. 点击"检查更新"按钮
4. 当前会显示连接错误（因为没有更新服务器）

### 2. 接口字段要求

`electron-updater` 使用 `generic` provider，需要返回特定格式的 YAML 文件。

#### 必需的接口端点：

1. **macOS**: `GET /updates/latest-mac.yml`
2. **Windows**: `GET /updates/latest.yml`
3. **Linux**: `GET /updates/latest-linux.yml`

#### 接口返回字段（YAML 格式）：

```yaml
# 必需字段
version: 1.6.0 # 新版本号（必需）
files: # 文件列表（必需）
  - url: app-1.6.0.dmg # 下载文件名（必需）
    sha512: abc123... # 文件SHA512校验值（必需）
    size: 100000000 # 文件大小（字节，必需）

# 可选字段
path: app-1.6.0.dmg # 主文件路径（可选）
sha512: abc123... # 主文件SHA512（可选）
releaseDate: '2025-06-18T11:00:00.000Z' # 发布日期（可选）
releaseNotes: | # 更新说明（可选，支持多行）
  ## 新功能
  - 添加了自动更新功能
  - 优化了用户界面

  ## 修复
  - 修复了已知问题
```

#### 当前更新逻辑使用的字段：

从代码分析，当前逻辑主要使用：
- `info.version` - 版本号（显示在UI中）
- `info.releaseNotes` - 更新说明（显示在对话框中）
- `info.releaseDate` - 发布日期（可选）

### 3. 搭建测试服务器

```bash
# 在另一个终端中运行
mkdir update-server && cd update-server
npm init -y
npm install express cors js-yaml
```

创建 `server.js`：
```javascript
const express = require('express')
const cors = require('cors')
const yaml = require('js-yaml')
const app = express()

app.use(cors())

// macOS 更新检查端点
app.get('/updates/latest-mac.yml', (req, res) => {
  const updateInfo = {
    version: '1.6.0',
    files: [
      {
        url: 'soybean-admin-1.6.0.dmg',
        sha512: 'abc123def456ghi789jkl012mno345pqr678stu901vwx234yz567890abcdef123456789',
        size: 150000000
      }
    ],
    path: 'soybean-admin-1.6.0.dmg',
    sha512: 'abc123def456ghi789jkl012mno345pqr678stu901vwx234yz567890abcdef123456789',
    releaseDate: new Date().toISOString(),
    releaseNotes: `## 新功能
- 添加了自动更新功能
- 优化了用户界面
- 增强了系统稳定性

## 修复
- 修复了已知的内存泄漏问题
- 解决了窗口焦点问题
- 优化了启动速度`
  }

  res.set('Content-Type', 'text/yaml')
  res.send(yaml.dump(updateInfo))
})

// Windows 更新检查端点
app.get('/updates/latest.yml', (req, res) => {
  const updateInfo = {
    version: '1.6.0',
    files: [
      {
        url: 'soybean-admin-1.6.0.exe',
        sha512: 'def456ghi789jkl012mno345pqr678stu901vwx234yz567890abcdef123456789abc',
        size: 120000000
      }
    ],
    path: 'soybean-admin-1.6.0.exe',
    sha512: 'def456ghi789jkl012mno345pqr678stu901vwx234yz567890abcdef123456789abc',
    releaseDate: new Date().toISOString(),
    releaseNotes: '新版本包含重要更新和修复'
  }

  res.set('Content-Type', 'text/yaml')
  res.send(yaml.dump(updateInfo))
})

app.listen(3000, () => {
  console.log('更新服务器运行在 http://localhost:3000')
  console.log('macOS: http://localhost:3000/updates/latest-mac.yml')
  console.log('Windows: http://localhost:3000/updates/latest.yml')
})
```

### 4. 接口返回示例

#### 有新版本时的返回：
```yaml
version: 1.6.0
files:
  - url: soybean-admin-1.6.0.dmg
    sha512: abc123def456ghi789jkl012mno345pqr678stu901vwx234yz567890abcdef123456789
    size: 150000000
path: soybean-admin-1.6.0.dmg
sha512: abc123def456ghi789jkl012mno345pqr678stu901vwx234yz567890abcdef123456789
releaseDate: '2025-06-18T11:00:00.000Z'
releaseNotes: |
  ## 新功能
  - 添加了自动更新功能
  - 优化了用户界面

  ## 修复
  - 修复了已知问题
```

#### 无新版本时：
返回 HTTP 404 或当前版本信息

#### 字段说明：
- **version**: 新版本号，必须大于当前版本才会触发更新
- **files[].url**: 安装包文件名，会与 updateServerUrl 拼接成完整下载地址
- **files[].sha512**: 文件校验值，用于验证下载完整性
- **files[].size**: 文件大小，用于显示下载进度
- **releaseNotes**: 更新说明，支持 Markdown 格式，会显示在更新对话框中

### 5. 配置说明

当前开发环境配置：
- **更新服务器**: `http://localhost:3000/updates`
- **检查间隔**: 5分钟（开发环境）
- **自动下载**: 禁用（便于测试）
- **自动更新**: 启用

### 6. 环境变量控制

可以通过环境变量控制更新行为：

```bash
# 禁用自动更新
DISABLE_AUTO_UPDATE=true pnpm dev

# 自定义更新服务器
UPDATE_SERVER_URL=https://your-server.com/updates pnpm dev
```

### 7. 测试步骤

1. **启动测试服务器**：
   ```bash
   cd update-server
   node server.js
   ```

2. **启动应用**：
   ```bash
   pnpm dev
   ```

3. **测试更新检查**：
   - 在应用中点击"检查更新"
   - 应该会显示发现新版本 1.6.0
   - 可以测试下载和安装流程

## 📁 相关文件

- `electron/main/config/update-config.ts` - 更新配置
- `electron/main/services/update-service.ts` - 更新服务
- `src/views/system/update/index.vue` - 更新管理页面
- `src/components/common/UpdateDialog.vue` - 更新对话框

## 🔧 生产环境部署

1. 配置真实的更新服务器 URL
2. 设置 `NODE_ENV=production`
3. 构建并发布应用：`pnpm build-electron`

## 📝 注意事项

- 开发环境中更新功能主要用于测试 UI 和逻辑
- 真实的更新需要打包的应用和有效的更新服务器
- 更新服务器需要提供符合 electron-updater 格式的元数据文件

现在您可以正常开发和测试更新功能了！🎉
