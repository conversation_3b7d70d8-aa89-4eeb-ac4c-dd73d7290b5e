version: '3.8'

services:
  electron-build:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: electron_builder
    working_dir: /project
    entrypoint: /bin/bash -c
    command: >
      "
      pnpm add -D cross-env &&
      pnpm install --frozen-lockfile --store-dir=/root/.pnpm-store &&
      ${BUILD_COMMAND}
      "
    environment:
      ELECTRON_CACHE: /root/.cache/electron
      ELECTRON_BUILDER_CACHE: /root/.cache/electron-builder
      PNPM_HOME: /root/.pnpm-store
    volumes:
      - ./:/project
      - project_node_modules:/project/node_modules
      - pnpm_store:/root/.pnpm-store
      - ~/.cache/electron:/root/.cache/electron
      - ~/.cache/electron-builder:/root/.cache/electron-builder

volumes:
  project_node_modules:
  pnpm_store:
