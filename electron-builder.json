{"productName": "咧嘴笑舌头", "appId": "com.soybean.admin", "asar": true, "directories": {"output": "release", "buildResources": "build"}, "files": ["dist-electron/**/*", "dist/**/*", "package.json", "!release/**/*", "!**/node_modules/**/*", "!src/**/*", "!electron/**/*", "!build/**/*", "!*.md", "!.vscode/**/*", "!.git/**/*", "!.husky/**/*", "!stats.html", "!pnpm-lock.yaml", "!pnpm-workspace.yaml", "!.eslintrc*", "!*.config.*", "!docs/**/*", "!patches/**/*", "!scripts/**/*", "!packages/**/*", "!**/*.map", "!**/*.d.ts", "!**/.DS_Store", "!.env*", "!.npmrc", "!.editorconfig", "!.giti<PERSON>re", "!.gitattributes", "!.cz-config.cjs", "!commitlint.config.js", "!electron-builder.json", "!**/*.log", "!**/*.tmp", "!**/*.temp", "!**/*.swp", "!**/*.swo", "!**/Thumbs.db", "!**/.cache", "!**/coverage/**/*", "!**/.nyc_output/**/*", "!**/cypress/**/*", "!**/test/**/*", "!**/__tests__/**/*", "!**/*.test.*", "!**/*.spec.*"], "extraResources": [{"from": "resources", "to": "resources", "filter": ["**/*"]}], "mac": {"category": "public.app-category.productivity", "icon": "build/electron/macOS.icns", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/electron/entitlements.mac.plist", "entitlementsInherit": "build/electron/entitlements.mac.plist", "identity": null, "target": [{"target": "dmg", "arch": ["arm64", "x64"]}, {"target": "zip", "arch": ["arm64", "x64"]}]}, "dmg": {"title": "${productName} ${version}", "icon": "build/electron/macOS.icns", "window": {"width": 540, "height": 380}}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "zip", "arch": ["x64"]}], "icon": "build/electron/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "${productName}"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "snap", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "build/electron/icon.png"}, "publish": {"provider": "generic", "url": "https://example.com/auto-updates"}}