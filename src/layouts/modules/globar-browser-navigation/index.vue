<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useDebounceFn } from '@vueuse/core'

defineOptions({
  name: 'GlobalBrowserNavigation',
})

const router = useRouter()

// 路由历史记录
const routeHistory = ref<string[]>([])
// 当前历史记录索引
const currentIndex = ref(-1)
// 是否正在进行导航操作（防止重复记录）
const isNavigating = ref(false)

// 计算是否可以后退
const canGoBack = computed(() => currentIndex.value > 0)

// 计算是否可以前进
const canGoForward = computed(() => currentIndex.value < routeHistory.value.length - 1)

// 添加路由到历史记录的函数
function addToHistory(newPath: string) {
  // 检查是否与当前历史记录中的最后一个路径相同，避免重复记录
  const lastPath = routeHistory.value[currentIndex.value]
  if (lastPath === newPath) {
    return
  }

  // 如果当前不在历史记录的末尾，删除后面的记录
  if (currentIndex.value < routeHistory.value.length - 1) {
    routeHistory.value = routeHistory.value.slice(0, currentIndex.value + 1)
  }

  // 检查新路径是否已存在于当前有效的历史记录中
  const existingIndex = routeHistory.value.findIndex(path => path === newPath)
  if (existingIndex !== -1 && existingIndex <= currentIndex.value) {
    // 如果路径已存在，移除旧的记录
    routeHistory.value.splice(existingIndex, 1)
    // 调整当前索引
    if (existingIndex <= currentIndex.value) {
      currentIndex.value--
    }
  }

  // 添加新路径到历史记录
  routeHistory.value.push(newPath)
  currentIndex.value = routeHistory.value.length - 1

  // 限制历史记录长度，避免内存泄漏
  if (routeHistory.value.length > 50) {
    routeHistory.value = routeHistory.value.slice(-50)
    currentIndex.value = routeHistory.value.length - 1
  }
}

// 监听路由变化，记录历史（使用防抖优化性能）
const debouncedAddToHistory = useDebounceFn(addToHistory, 100)

watch(
  () => router.currentRoute.value.fullPath,
  (newPath) => {
    if (isNavigating.value) {
      isNavigating.value = false
      return
    }
    debouncedAddToHistory(newPath)
  },
  { immediate: true },
)

// 后退功能
function goBack() {
  if (!canGoBack.value)
    return

  isNavigating.value = true
  currentIndex.value--
  const targetPath = routeHistory.value[currentIndex.value]
  router.push(targetPath).catch(() => {
    // 如果导航失败，恢复索引
    currentIndex.value++
    isNavigating.value = false
  })
}

// 前进功能
function goForward() {
  if (!canGoForward.value)
    return

  isNavigating.value = true
  currentIndex.value++
  const targetPath = routeHistory.value[currentIndex.value]
  router.push(targetPath).catch(() => {
    // 如果导航失败，恢复索引
    currentIndex.value--
    isNavigating.value = false
  })
}

// 计算后退按钮的样式类
const backButtonClass = computed(() =>
  !canGoBack.value ? 'opacity-50 cursor-not-allowed' : '',
)

// 计算前进按钮的样式类
const forwardButtonClass = computed(() =>
  !canGoForward.value ? 'opacity-50 cursor-not-allowed' : '',
)

// 调试信息（开发环境下可用）
const debugInfo = computed(() => ({
  historyLength: routeHistory.value.length,
  currentIndex: currentIndex.value,
  currentPath: routeHistory.value[currentIndex.value],
  canGoBack: canGoBack.value,
  canGoForward: canGoForward.value,
  history: routeHistory.value,
}))

// 在开发环境下输出调试信息
if (import.meta.env.DEV) {
  watch(debugInfo, (info) => {
    // eslint-disable-next-line no-console
    console.log('🧭 Browser Navigation Debug:', info)
  }, { deep: true })
}
</script>

<template>
  <div class="flex items-center gap-1">
    <!-- 后退按钮 -->
    <ButtonIcon
      :class="backButtonClass"
      :tooltip-content="`后退${canGoBack ? ` - ${routeHistory[currentIndex - 1]?.split('/').pop() || ''}` : ''}`"
      @click="goBack"
    >
      <icon-mdi-arrow-left class="text-16px" />
    </ButtonIcon>

    <!-- 前进按钮 -->
    <ButtonIcon
      :class="forwardButtonClass"
      :tooltip-content="`前进${canGoForward ? ` - ${routeHistory[currentIndex + 1]?.split('/').pop() || ''}` : ''}`"
      @click="goForward"
    >
      <icon-mdi-arrow-right class="text-16px" />
    </ButtonIcon>
  </div>
</template>

<style lang="scss" scoped>

</style>
