<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { NAlert, NModal, NProgress } from 'naive-ui'
import { UpdateStatus, electronUpdateAPI, formatBytes, formatSpeed, formatUpdateStatus } from '@/utils/electron-update'
import type { UpdateInfo } from '@/utils/electron-update'

interface Props {
  show: boolean
  updateInfo: UpdateInfo | null
  currentVersion: string
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'download'): void
  (e: 'install'): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isLoading = ref(false)

// 计算属性
const showModal = computed({
  get: () => props.show,
  set: value => emit('update:show', value),
})

const dialogTitle = computed(() => {
  if (!props.updateInfo)
    return '检查更新'

  switch (props.updateInfo.status) {
    case UpdateStatus.CHECKING:
      return '正在检查更新...'
    case UpdateStatus.AVAILABLE:
      return '发现新版本'
    case UpdateStatus.DOWNLOADING:
      return '正在下载更新'
    case UpdateStatus.DOWNLOADED:
      return '更新下载完成'
    case UpdateStatus.ERROR:
      return '更新出错'
    case UpdateStatus.INSTALLING:
      return '正在安装更新'
    default:
      return '检查更新'
  }
})

const positiveText = computed(() => {
  if (!props.updateInfo)
    return '确定'

  switch (props.updateInfo.status) {
    case UpdateStatus.AVAILABLE:
      return '立即下载'
    case UpdateStatus.DOWNLOADED:
      return '立即安装'
    case UpdateStatus.ERROR:
      return '重试'
    default:
      return '确定'
  }
})

const negativeText = computed(() => {
  if (!props.updateInfo)
    return '取消'

  switch (props.updateInfo.status) {
    case UpdateStatus.AVAILABLE:
      return '稍后提醒'
    case UpdateStatus.DOWNLOADING:
      return '取消下载'
    case UpdateStatus.DOWNLOADED:
      return '稍后安装'
    default:
      return '取消'
  }
})

const showProgress = computed(() => {
  return props.updateInfo?.status === UpdateStatus.DOWNLOADING && props.updateInfo.progress
})

const progressText = computed(() => {
  if (!props.updateInfo?.progress)
    return ''

  const { percent, bytesPerSecond } = props.updateInfo.progress
  return `正在下载更新... ${Math.round(percent)}%`
})

const statusText = computed(() => {
  if (!props.updateInfo)
    return ''
  return formatUpdateStatus(props.updateInfo.status)
})

// 方法
async function handlePositiveClick() {
  if (!props.updateInfo) {
    emit('update:show', false)
    return
  }

  isLoading.value = true

  try {
    switch (props.updateInfo.status) {
      case UpdateStatus.AVAILABLE:
        emit('download')
        break
      case UpdateStatus.DOWNLOADED:
        emit('install')
        break
      case UpdateStatus.ERROR:
        // 重试检查更新
        await electronUpdateAPI.checkForUpdates()
        break
      default:
        emit('update:show', false)
        break
    }
  }
  finally {
    isLoading.value = false
  }
}

function handleNegativeClick() {
  emit('cancel')
  emit('update:show', false)
}

function formatReleaseNotes(notes: string): string {
  // 简单的 Markdown 转换
  return notes
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
}

// 监听更新状态变化
watch(() => props.updateInfo?.status, (newStatus) => {
  if (newStatus === UpdateStatus.INSTALLING) {
    isLoading.value = true
  }
  else {
    isLoading.value = false
  }
})
</script>

<template>
  <NModal
    v-model:show="showModal"
    :mask-closable="false"
    :close-on-esc="false"
    preset="dialog"
    :title="dialogTitle"
    :positive-text="positiveText"
    :negative-text="negativeText"
    :loading="isLoading"
    @positive-click="handlePositiveClick"
    @negative-click="handleNegativeClick"
  >
    <div class="update-dialog">
      <!-- 更新信息 -->
      <div v-if="updateInfo" class="update-info">
        <div class="version-info">
          <p><strong>当前版本：</strong>{{ currentVersion }}</p>
          <p><strong>最新版本：</strong>{{ updateInfo.version }}</p>
        </div>

        <!-- 更新说明 -->
        <div v-if="updateInfo.releaseNotes" class="release-notes">
          <h4>更新说明：</h4>
          <div class="notes-content" v-html="formatReleaseNotes(updateInfo.releaseNotes)" />
        </div>
      </div>

      <!-- 下载进度 -->
      <div v-if="showProgress" class="download-progress">
        <div class="progress-info">
          <span>{{ progressText }}</span>
          <span v-if="updateInfo?.progress">{{ Math.round(updateInfo.progress.percent) }}%</span>
        </div>

        <NProgress
          v-if="updateInfo?.progress"
          type="line"
          :percentage="updateInfo.progress.percent"
          :show-indicator="false"
          status="success"
        />

        <div v-if="updateInfo?.progress" class="speed-info">
          <span>下载速度: {{ formatSpeed(updateInfo.progress.bytesPerSecond) }}</span>
          <span>已下载: {{ formatBytes(updateInfo.progress.transferred) }} / {{ formatBytes(updateInfo.progress.total) }}</span>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="updateInfo?.error" class="error-info">
        <NAlert type="error" :title="updateInfo.error" />
      </div>

      <!-- 状态信息 -->
      <div class="status-info">
        <p>{{ statusText }}</p>
      </div>
    </div>
  </NModal>
</template>

<style scoped>
.update-dialog {
  padding: 16px 0;
}

.update-info {
  margin-bottom: 16px;
}

.version-info {
  margin-bottom: 16px;
}

.version-info p {
  margin: 8px 0;
  font-size: 14px;
}

.release-notes {
  margin-top: 16px;
}

.release-notes h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.notes-content {
  padding: 12px;
  background-color: var(--n-color-target);
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.5;
  max-height: 200px;
  overflow-y: auto;
}

.download-progress {
  margin: 16px 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.speed-info {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: var(--n-text-color-2);
}

.error-info {
  margin: 16px 0;
}

.status-info {
  margin-top: 16px;
  text-align: center;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.status-info p {
  margin: 0;
}

:deep(code) {
  background-color: var(--n-code-color);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: var(--n-font-family-mono);
  font-size: 12px;
}
</style>
