<script setup lang="ts">
import {
  NCheckbox,
  NCheckboxGroup,
  NDatePicker,
  NInput,
  NRadio,
  NRadioButton,
  NRadioGroup,
  NSelect,
  NSwitch,
  NUpload,
} from 'naive-ui'
import type { FormOptions } from './types/types'

const props = defineProps({
  // 表单配置项
  options: {
    type: Array as PropType<FormOptions[]>,
    required: true,
  },
  // 自定义上传请求。
  customRequest: {
    type: Function,
  },
})

const emits = defineEmits(['onPreview', 'onError'])
// const formRef = ref<FormInst | null>(null)

const componentMap = {
  input: NInput,
  select: NSelect,
  radio: NRadio,
  radioGroup: NRadioGroup,
  checkbox: NCheckbox,
  checkboxGroup: NCheckboxGroup,
  datePicker: NDatePicker,
  switch: NSwitch,
  radioButton: NRadioButton,
  upload: NUpload,
}
// 上传文件预览和错误处理函数
function onPreviewHandler(file: any) {
  console.log(file)
}
// 上传文件错误处理函数
function onErrorHandler(e: any) {
  emits('onError', e)
}
const formModel = ref<any>({})
const rules = ref<any>({})
function initFrom() {
  // 初始化表单数据对象和验证规则对象
  const formData: any = {}
  const formRules: any = {}

  // 遍历配置项，设置初始值和验证规则
  props.options.forEach((item) => {
    // 设置表单字段的初始值
    formData[item.path!] = item.value
    formRules[item.path!] = item.rules
  })

  // 更新响应式数据
  formModel.value = JSON.parse(JSON.stringify(formData))
  rules.value = JSON.parse(JSON.stringify(formRules))
  console.log(formModel.value)
  console.log(rules.value)
}
onMounted(() => {
  initFrom()
})
watch(
  () => props.options,
  () => {
    initFrom()
  },
  {
    deep: true,
  },
)
</script>

<template>
  <n-form :model="formModel" :rules="rules" v-bind="$attrs">
    <template v-for="(item, index) in props.options" :key="index">
      <n-form-item
        v-if="!item.children || !item.children!.length"
        :label="item.label"
        :path="item.path"
      >
        <component
          :is="componentMap[item.type as keyof typeof componentMap]"
          v-if="item.type !== 'upload'"
          v-model:value="formModel[item.path!]"
          :placeholder="item.placeholder"
          v-bind="item.attrs"
        />
        <NUpload
          v-else
          v-bind="item.uploadAttrs"
          :on-preview="onPreviewHandler"
          :on-error="onErrorHandler"
          :custom-request="props.customRequest as import('naive-ui').CustomRequest"
        >
          <slot name="uploadArea" />
          <slot name="uploadTip" />
        </NUpload>
      </n-form-item>
      <n-form-item
        v-if="item.children && item.children.length"
        :label="item.label"
        :path="item.path"
      >
        <component
          :is="componentMap[item.type as keyof typeof componentMap]"
          v-model:value="formModel[item.path!]"
          :placeholder="item.placeholder"
          v-bind="item.attrs"
        >
          <component
            :is="componentMap[childItem.type as keyof typeof componentMap]"
            v-for="(childItem, childIndex) in item.children"
            :key="childIndex"
            :label="childItem.label"
            :value="childItem.value"
          />
        </component>
      </n-form-item>
    </template>
    <slot name="footer" />
  </n-form>
</template>
