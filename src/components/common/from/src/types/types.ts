// 表单每一项的配置
import type { CSSProperties } from 'vue'
import type { RuleItem } from './rule'

export interface FormOptions {
  // 表单每一项的类型
  type: 'input' |
    'cascader' |
    'select' |
    'checkboxGroup' |
    'checkbox' |
    'radioGroup' |
    'radio' |
    'datePicker' |
    'switch' |
    'radioButton' |
    'upload'
  // 表单每一项的默认值
  value?: any
  // 表单每一项的label
  label?: string
  // 表单每一项的占位符
  placeholder?: string
  // 表单每一项的选项 用来做下拉框的选项
  options?: Array<{
    label: string
    value: string | number
    disabled?: boolean
  }>
  // 表单验证的路径
  path?: string
  // 表单每一项的验证规则
  rules?: RuleItem[]
  // 表单元素特有的属性 暂时给any类型
  attrs?: {
    clearable?: boolean // 是否可以清空
    disabled?: boolean // 是否禁用
    type?: 'text' | 'password' | 'textarea' | 'datetime'
    showPasswordOn?: 'click' | 'mousedown'
    placeholder?: string
    multiple?: boolean // 是否多选
    // 表单每一项的选项 用来做下拉框的选项
    options?: Array<{
      label: string
      value: string | number
      disabled?: boolean
    }>
    // css样式
    style?: CSSProperties
  }
  // 表单每一项的子元素
  children?: FormOptions[]
  uploadAttrs?: {
    action: string
    headers?: any
    data?: any
    method?: string
    multiple?: boolean // 是否支持多个文件
  }
}
