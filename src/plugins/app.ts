import { h } from 'vue'
import type { App } from 'vue'
import { NButton } from 'naive-ui'

/**
 * 设置应用错误处理
 * @param app Vue应用实例
 * @description 全局捕获Vue应用中的错误并打印到控制台
 */
export function setupAppErrorHandle(app: App) {
  app.config.errorHandler = (err, vm, info) => {
    console.error('Vue应用错误:', err, '组件实例:', vm, '错误信息:', info)
  }
}

/**
 * 设置应用版本更新通知
 * @description 定时检查应用是否有新版本，如果有则弹出更新提示
 * 1. 仅在生产环境且配置允许时启用自动更新检查
 * 2. 每3分钟检查一次index.html的buildTime是否变化
 * 3. 检测到更新时显示通知，用户可选择立即刷新或稍后处理
 */
export function setupAppVersionNotification() {
  // 更新检查间隔时间（3分钟）
  const UPDATE_CHECK_INTERVAL = 3 * 60 * 1000

  // 检查是否允许自动更新：生产环境且配置为Y
  const canAutoUpdateApp = import.meta.env.VITE_AUTOMATICALLY_DETECT_UPDATE === 'Y' && import.meta.env.PROD
  if (!canAutoUpdateApp)
    return

  let isShow = false // 是否正在显示更新通知
  let updateInterval: ReturnType<typeof setInterval> | undefined // 定时器引用

  /**
   * 检查应用是否有更新
   * @description 通过比较当前buildTime和服务器上的buildTime判断是否需要更新
   */
  const checkForUpdates = async () => {
    if (isShow) // 如果已经在显示更新通知，则不再重复检查
      return

    const buildTime = await getHtmlBuildTime()

    // 如果buildTime没有变化，不需要更新
    if (buildTime === BUILD_TIME) {
      return
    }

    isShow = true // 标记为正在显示通知

    // 创建更新通知
    const n = window.$notification?.create({
      title: '系统版本更新通知',
      content: '检测到系统有新版本发布，是否立即刷新页面？',
      action() {
        // 渲染通知操作按钮
        return h('div', {
          style: {
            display: 'flex',
            justifyContent: 'end',
            gap: '12px',
            width: '325px',
          },
        }, [
          // 稍后再说按钮
          h(
            NButton,
            {
              onClick() {
                n?.destroy()
                isShow = false
              },
            },
            () => '稍后再说',
          ),
          // 立即刷新按钮
          h(
            NButton,
            {
              type: 'primary',
              onClick() {
                location.reload()
              },
            },
            () => '立即刷新',
          ),
        ])
      },
      onClose() {
        isShow = false // 关闭时重置状态
      },
    })
  }

  /**
   * 启动更新检查定时器
   */
  const startUpdateInterval = () => {
    if (updateInterval) {
      clearInterval(updateInterval) // 清除已有定时器
    }
    updateInterval = setInterval(checkForUpdates, UPDATE_CHECK_INTERVAL) // 设置新定时器
  }

  // 当页面可见时启动更新检查
  if (!isShow && document.visibilityState === 'visible') {
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        checkForUpdates() // 页面变为可见时立即检查
        startUpdateInterval() // 启动定时检查
      }
    })

    // 初始启动定时检查
    startUpdateInterval()
  }
}

/**
 * 获取HTML文件的构建时间
 * @returns 返回从index.html中解析出的buildTime
 * @description 通过请求index.html并解析其中的meta标签获取构建时间
 */
async function getHtmlBuildTime() {
  const baseUrl = import.meta.env.VITE_BASE_URL || '/'

  // 添加时间戳参数避免缓存
  const res = await fetch(`${baseUrl}index.html?time=${Date.now()}`)

  const html = await res.text()

  // 从HTML中匹配buildTime meta标签
  const match = html.match(/<meta name="buildTime" content="(.*)">/)

  const buildTime = match?.[1] || ''

  return buildTime
}
