import { addAPIProvider, disableCache } from '@iconify/vue'

/**
 * 设置Iconify离线模式
 * @description 配置Iconify使用本地资源并禁用缓存
 * 1. 从环境变量获取图标资源URL
 * 2. 如果配置了URL，则设置为API提供者资源
 * 3. 禁用所有缓存确保使用最新资源
 */
export function setupIconifyOffline() {
  // 从环境变量获取图标资源URL
  const { VITE_ICONIFY_URL } = import.meta.env

  // 检查是否配置了图标资源URL
  if (VITE_ICONIFY_URL) {
    // 添加API提供者，使用配置的URL作为资源
    addAPIProvider('', { resources: [VITE_ICONIFY_URL] })

    // 禁用所有缓存
    disableCache('all')
  }
}
