// 导入认证状态仓库
import { useAuthStore } from '@/store/modules/auth'

/**
 * 认证权限Hook
 * @returns 包含权限检查方法的对象
 */
export function useAuth() {
  // 获取认证状态仓库实例
  const authStore = useAuthStore()

  /**
   * 检查是否拥有指定权限码
   * @param codes 单个权限码或权限码数组
   * @returns 是否拥有权限
   */
  function hasAuth(codes: string | string[]) {
    // 未登录直接返回无权限
    if (!authStore.isLogin) {
      return false
    }

    // 处理单个权限码情况
    if (typeof codes === 'string') {
      return authStore.userInfo.buttons.includes(codes)
    }

    // 处理多个权限码情况(任一匹配即返回true)
    return codes.some(code => authStore.userInfo.buttons.includes(code))
  }

  // 返回对外暴露的方法
  return {
    hasAuth,
  }
}
