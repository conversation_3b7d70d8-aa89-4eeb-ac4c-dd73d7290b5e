import { computed } from 'vue'
import { useCountDown, useLoading } from '@sa/hooks'
import { REG_PHONE } from '@/constants/reg'

/**
 * 验证码功能Hook
 * @returns 包含验证码相关状态和方法的对象
 */
export function useCaptcha() {
  // 加载状态控制
  const { loading, startLoading, endLoading } = useLoading()
  // 倒计时功能(默认60秒)
  const { count, start, stop, isCounting } = useCountDown(10)

  // 计算按钮显示文本
  const label = computed(() => {
    let text = '获取验证码'
    const countingLabel = `${count.value}秒后重新获取`

    if (loading.value) {
      text = '' // 加载中不显示文本
    }

    if (isCounting.value) {
      text = countingLabel // 显示倒计时
    }

    return text
  })

  /**
   * 验证手机号格式
   * @param phone 手机号字符串
   * @returns 是否有效
   */
  function isPhoneValid(phone: string) {
    if (phone.trim() === '') {
      window.$message?.error?.('请输入手机号')
      return false
    }

    if (!REG_PHONE.test(phone)) {
      window.$message?.error?.('手机号格式不正确')
      return false
    }

    return true
  }

  /**
   * 获取验证码
   * @param phone 手机号
   */
  async function getCaptcha(phone: string) {
    const valid = isPhoneValid(phone)

    if (!valid || loading.value) {
      return // 无效或正在加载时直接返回
    }

    startLoading() // 开始加载

    // 模拟API请求
    await new Promise((resolve) => {
      setTimeout(resolve, 500)
    })

    window.$message?.success?.('验证码发送成功') // 提示成功
    start() // 开始倒计时
    endLoading() // 结束加载
  }

  // 暴露给组件的API
  return {
    label, // 按钮文本
    start, // 开始倒计时方法
    stop, // 停止倒计时方法
    isCounting, // 是否正在倒计时
    loading, // 是否正在加载
    getCaptcha, // 获取验证码方法
  }
}
