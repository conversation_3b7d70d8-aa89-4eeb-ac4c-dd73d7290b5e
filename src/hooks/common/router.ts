// 导入Vue Router相关API和类型
import { useRouter } from 'vue-router'
import type { RouteLocationRaw } from 'vue-router'
import type { RouteKey } from '@elegant-router/types'
import { router as globalRouter } from '@/router'

/**
 * 路由跳转Hook
 * @param inSetup 是否在setup中使用，默认为true
 */
export function useRouterPush(inSetup = true) {
  // 根据使用场景选择路由实例
  const router = inSetup ? useRouter() : globalRouter
  // 获取当前路由信息
  const route = globalRouter.currentRoute

  // 原生路由跳转方法
  const routerPush = router.push
  // 原生路由返回方法
  const routerBack = router.back

  // 路由跳转选项接口
  interface RouterPushOptions {
    query?: Record<string, string>
    params?: Record<string, string>
  }

  /**
   * 通过路由key跳转
   * @param key 路由key
   * @param options 跳转选项
   */
  async function routerPushByKey(key: RouteKey, options?: RouterPushOptions) {
    const { query, params } = options || {}

    const routeLocation: RouteLocationRaw = {
      name: key,
    }

    // 添加查询参数
    if (Object.keys(query || {}).length) {
      routeLocation.query = query
    }

    // 添加路由参数
    if (Object.keys(params || {}).length) {
      routeLocation.params = params
    }

    return routerPush(routeLocation)
  }

  /**
   * 通过路由key跳转并携带meta中的query参数
   * @param key 路由key
   */
  function routerPushByKeyWithMetaQuery(key: RouteKey) {
    const allRoutes = router.getRoutes()
    const meta = allRoutes.find(item => item.name === key)?.meta || null

    // 从meta中提取query参数
    const query: Record<string, string> = {}

    meta?.query?.forEach((item) => {
      query[item.key] = item.value
    })

    return routerPushByKey(key, { query })
  }

  // 跳转到首页
  async function toHome() {
    return routerPushByKey('root')
  }

  /**
   * 跳转到登录页
   * @param loginModule 登录模块类型
   * @param redirectUrl 重定向URL，默认为当前路由
   */
  async function toLogin(loginModule?: UnionKey.LoginModule, redirectUrl?: string) {
    const module = loginModule || 'pwd-login'

    const options: RouterPushOptions = {
      params: {
        module,
      },
    }

    const redirect = redirectUrl || route.value.fullPath

    options.query = {
      redirect,
    }

    return routerPushByKey('login', options)
  }

  /**
   * 切换登录模块
   * @param module 登录模块类型
   */
  async function toggleLoginModule(module: UnionKey.LoginModule) {
    const query = route.value.query as Record<string, string>

    return routerPushByKey('login', { query, params: { module } })
  }

  /**
   * 从登录页重定向
   * @param needRedirect 是否需要重定向，默认为true
   */
  async function redirectFromLogin(needRedirect = true) {
    const redirect = route.value.query?.redirect as string

    if (needRedirect && redirect) {
      await routerPush(redirect)
    }
    else {
      await toHome()
    }
  }

  // 暴露给组件的API
  return {
    routerPush, // 原生跳转方法
    routerBack, // 原生返回方法
    routerPushByKey, // 通过key跳转
    routerPushByKeyWithMetaQuery, // 通过key跳转并携带meta参数
    toLogin, // 跳转登录页
    toggleLoginModule, // 切换登录模块
    redirectFromLogin, // 从登录页重定向
  }
}
