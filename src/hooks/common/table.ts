import { computed, effectScope, onScopeDispose, reactive, ref } from 'vue'
import type { Ref } from 'vue'
import type { PaginationProps } from 'naive-ui'
import { jsonClone } from '@sa/utils'
import { useBoolean, useHookTable } from '@sa/hooks'
import { useAppStore } from '@/store/modules/app'

type TableData = NaiveUI.TableData
type GetTableData<A extends NaiveUI.TableApiFn> = NaiveUI.GetTableData<A>
type TableColumn<T> = NaiveUI.TableColumn<T>

/**
 * 表格Hook函数
 * @param config 表格配置项
 * @description 提供表格数据管理、分页、列配置等功能
 */
export function useTable<A extends NaiveUI.TableApiFn>(config: NaiveUI.NaiveTableConfig<A>) {
  // 创建effect作用域
  const scope = effectScope()
  const appStore = useAppStore()

  // 响应式计算是否是移动端
  const isMobile = computed(() => appStore.isMobile)

  // 解构配置参数
  const { apiFn, apiParams, immediate, showTotal } = config

  // 定义特殊列类型的key
  const SELECTION_KEY = '__selection__'
  const EXPAND_KEY = '__expand__'

  // 使用基础Hook获取表格数据
  const {
    loading,
    empty,
    data,
    columns,
    columnChecks,
    reloadColumns,
    getData,
    searchParams,
    updateSearchParams,
    resetSearchParams,
  } = useHookTable<A, GetTableData<A>, TableColumn<NaiveUI.TableDataWithIndex<GetTableData<A>>>>({
    apiFn,
    apiParams,
    columns: config.columns,
    // 数据转换器
    transformer: (res) => {
      const { records = [], current = 1, size = 10, total = 0 } = res.data || {}

      // 确保每页大小大于0
      const pageSize = size <= 0 ? 10 : size

      // 为每条记录添加索引
      const recordsWithIndex = records.map((item, index) => {
        return {
          ...item,
          index: (current - 1) * pageSize + index + 1,
        }
      })

      return {
        data: recordsWithIndex,
        pageNum: current,
        pageSize,
        total,
      }
    },
    // 获取列显示配置
    getColumnChecks: (cols) => {
      const checks: NaiveUI.TableColumnCheck[] = []

      cols.forEach((column) => {
        if (isTableColumnHasKey(column)) {
          checks.push({
            key: column.key as string,
            title: column.title as string,
            checked: true,
          })
        }
        else if (column.type === 'selection') {
          checks.push({
            key: SELECTION_KEY,
            title: '勾选',
            checked: true,
          })
        }
        else if (column.type === 'expand') {
          checks.push({
            key: EXPAND_KEY,
            title: '展开列',
            checked: true,
          })
        }
      })

      return checks
    },
    // 获取过滤后的列配置
    getColumns: (cols, checks) => {
      const columnMap = new Map<string, TableColumn<GetTableData<A>>>()

      cols.forEach((column) => {
        if (isTableColumnHasKey(column)) {
          columnMap.set(column.key as string, column)
        }
        else if (column.type === 'selection') {
          columnMap.set(SELECTION_KEY, column)
        }
        else if (column.type === 'expand') {
          columnMap.set(EXPAND_KEY, column)
        }
      })

      // 根据勾选状态过滤列
      const filteredColumns = checks
        .filter(item => item.checked)
        .map(check => columnMap.get(check.key) as TableColumn<GetTableData<A>>)

      return filteredColumns
    },

    // 数据获取后的回调
    onFetched: async (transformed) => {
      const { pageNum, pageSize, total } = transformed

      updatePagination({
        page: pageNum,
        pageSize,
        itemCount: total,
      })
    },
    immediate,
  })

  // 分页配置
  const pagination: PaginationProps = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    itemCount: 0,
    pageSizes: [10, 15, 20, 25, 30],
    // 页码变化回调
    onUpdatePage: async (page: number) => {
      pagination.page = page

      updateSearchParams({
        current: page,
        size: pagination.pageSize!,
      })

      getData()
    },
    // 页码变化回调
    onUpdatePageSize: async (pageSize: number) => {
      pagination.pageSize = pageSize
      pagination.page = 1

      updateSearchParams({
        current: pagination.page,
        size: pageSize,
      })

      getData()
    },
    // 是否显示总数
    ...(showTotal
      ? {
          prefix: page => `共 ${page.itemCount} 条`,
        }
      : {}),
  })

  // 每页大小变化回调
  const mobilePagination = computed(() => {
    const p: PaginationProps = {
      ...pagination,
      pageSlot: isMobile.value ? 3 : 9,
      prefix: !isMobile.value && showTotal ? pagination.prefix : undefined,
    }

    return p
  })

  // 更新分页配置
  function updatePagination(update: Partial<PaginationProps>) {
    Object.assign(pagination, update)
  }

  /**
   * 按页码获取数据
   * @param pageNum 页码，默认为1
   */
  async function getDataByPage(pageNum: number = 1) {
    updatePagination({
      page: pageNum,
    })

    updateSearchParams({
      current: pageNum,
      size: pagination.pageSize!,
    })

    await getData()
  }

  // scope.run(() => {
  //   watch(
  //     () => appStore.locale,
  //     () => {
  //       reloadColumns()
  //     },
  //   )
  // })
  // 组件卸载时停止effect作用域
  onScopeDispose(() => {
    scope.stop()
  })

  return {
    loading,
    empty,
    data,
    columns,
    columnChecks,
    reloadColumns,
    pagination,
    mobilePagination,
    updatePagination,
    getData,
    getDataByPage,
    searchParams,
    updateSearchParams,
    resetSearchParams,
  }
}

/**
 * 表格操作Hook
 * @param data 表格数据引用
 * @param getData 获取数据的函数
 */
export function useTableOperate<T extends TableData = TableData>(data: Ref<T[]>, getData: () => Promise<void>) {
  // 抽屉显示状态控制
  const { bool: drawerVisible, setTrue: openDrawer, setFalse: closeDrawer } = useBoolean()

  // 操作类型
  const operateType = ref<NaiveUI.TableOperateType>('add')

  // 添加操作
  function handleAdd() {
    operateType.value = 'add'
    openDrawer()
  }

  // 编辑数据引用
  const editingData: Ref<T | null> = ref(null)

  // 编辑操作
  function handleEdit(id: T['id']) {
    operateType.value = 'edit'
    const findItem = data.value.find(item => item.id === id) || null
    editingData.value = jsonClone(findItem)

    openDrawer()
  }

  // 选中行keys
  const checkedRowKeys = ref<string[]>([])

  // 批量删除完成回调
  async function onBatchDeleted() {
    window.$message?.success('删除成功')

    checkedRowKeys.value = []

    await getData()
  }

  // 单个删除完成回调
  async function onDeleted() {
    window.$message?.success('删除成功')

    await getData()
  }

  return {
    drawerVisible,
    openDrawer,
    closeDrawer,
    operateType,
    handleAdd,
    editingData,
    handleEdit,
    checkedRowKeys,
    onBatchDeleted,
    onDeleted,
  }
}
/**
 * 检查列是否有key属性
 */
function isTableColumnHasKey<T>(column: TableColumn<T>): column is NaiveUI.TableColumnWithKey<T> {
  return Boolean((column as NaiveUI.TableColumnWithKey<T>).key)
}
