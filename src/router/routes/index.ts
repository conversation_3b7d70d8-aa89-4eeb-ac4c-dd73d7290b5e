import type { CustomRoute, ElegantConstRoute, ElegantRoute } from '@elegant-router/types'
import { generatedRoutes } from '../elegant/routes'
import { layouts, views } from '../elegant/imports'
import { transformElegantRoutesToVueRoutes } from '../elegant/transform'

/**
 * 自定义路由配置
 * @description 用于添加项目特定的路由配置
 * @link https://github.com/soybeanjs/elegant-router?tab=readme-ov-file#custom-route
 */
const customRoutes: CustomRoute[] = []

/**
 * 创建静态路由
 * @returns 返回包含常量路由和权限路由的对象
 * @description 在静态路由模式下初始化路由配置
 */
export function createStaticRoutes() {
  const constantRoutes: ElegantRoute[] = []
  const authRoutes: ElegantRoute[] = [];

  // 合并自定义路由和生成的路由，并按类型分类
  [...customRoutes, ...generatedRoutes].forEach((item) => {
    if (item.meta?.constant) {
      constantRoutes.push(item) // 添加到常量路由
    }
    else {
      authRoutes.push(item) // 添加到权限路由
    }
  })

  return {
    constantRoutes, // 不需要权限的常量路由
    authRoutes, // 需要权限控制的路由
  }
}

/**
 * 获取权限路由的Vue路由配置
 * @param routes 优雅路由配置数组
 * @returns 返回转换后的Vue路由数组
 * @description 将优雅路由配置转换为Vue路由配置
 */
export function getAuthVueRoutes(routes: ElegantConstRoute[]) {
  return transformElegantRoutesToVueRoutes(routes, layouts, views)
}
