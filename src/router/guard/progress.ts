import type { Router } from 'vue-router'

/**
 * 创建页面加载进度条守卫
 * @param router Vue Router 实例
 * @description 配置路由导航时的进度条显示逻辑
 * 1. 在路由跳转前启动进度条(NProgress.start)
 * 2. 在路由跳转完成后结束进度条(NProgress.done)
 * 3. 使用可选链操作符(?.)确保NProgress存在
 */
export function createProgressGuard(router: Router) {
  // 路由跳转前回调 - 开始进度条
  router.beforeEach((_to, _from, next) => {
    window.NProgress?.start?.()
    next()
  })

  // 路由跳转后回调 - 结束进度条
  router.afterEach((_to) => {
    window.NProgress?.done?.()
  })
}
