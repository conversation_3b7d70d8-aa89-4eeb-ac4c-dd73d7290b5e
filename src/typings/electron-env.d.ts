/// <reference types="vite-plugin-electron/electron-env" />
// 声明 NodeJS 命名空间
declare namespace NodeJS {
  // 扩展 ProcessEnv 接口，定义环境变量的类型
  interface ProcessEnv {
    /**
     * 是否启用 VSCode 调试模式。
     * 如果存在该变量且值为 'true'，表示启用了调试模式。
     */
    VSCODE_DEBUG?: 'true'
    /**
     * 应用程序的构建目录结构说明。
     *
     * 目录结构示例：
     * ```tree
     * ├─┬ dist-electron
     * │ ├─┬ main
     * │ │ └── index.js    > Electron 主进程入口文件
     * │ └─┬ preload
     * │   └── index.mjs   > 预加载脚本文件
     * ├─┬ dist
     * │ └── index.html    > Electron 渲染进程入口文件
     * ```
     */
    APP_ROOT: string
    /**
     * 构建输出的公共资源目录路径。
     * 可能的值为 '/dist/' 或 '/public/'。
     */
    VITE_PUBLIC: string
  }
}
