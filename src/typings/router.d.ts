import 'vue-router'

declare module 'vue-router' {
  interface RouteMeta {
    /**
     * 路由标题
     *
     * 可以在文档标题中使用
     */
    title: string
    /**
     * 路由角色
     *
     * 如果当前用户具有至少一个角色，则可以访问该路由
     *
     * 仅在路由模式为 "static" 时有效，如果路由模式为 "dynamic"，则会被忽略
     */
    roles?: string[]
    /** 是否缓存路由 */
    keepAlive?: boolean | null
    /**
     * 是否为常量路由
     *
     * 当设置为 true 时，将不会进行登录验证和权限验证即可访问该路由
     */
    constant?: boolean | null
    /**
     * Iconify 图标
     *
     * 可以在菜单或面包屑中使用
     */
    icon?: string
    /**
     * 本地图标
     *
     * 在 "src/assets/svg-icon" 中，如果设置，则忽略 icon
     */
    localIcon?: string
    /** 图标大小。宽度和高度相同。 */
    iconFontSize?: number
    /** 路由顺序 */
    order?: number | null
    /** 路由的外部链接 */
    href?: string | null
    /** 是否在菜单中隐藏路由 */
    hideInMenu?: boolean | null
    /**
     * 进入路由时激活的菜单键
     *
     * 路由不在菜单中
     *
     * @example
     *   路由为 "user_detail"，如果设置为 "user_list"，则菜单 "user_list" 将被激活
     */
    activeMenu?: import('@elegant-router/types').RouteKey | null
    /**
     * 默认情况下，相同的路由路径将使用一个标签，即使查询参数不同，如果设置为 true，则查询参数不同的路由将使用不同的标签
     */
    multiTab?: boolean | null
    /** 如果设置，路由将在标签中固定，并且值为固定标签的顺序 */
    fixedIndexInTab?: number | null
    /** 如果设置查询参数，进入路由时将自动携带这些参数 */
    query?: { key: string, value: string }[] | null
  }
}
