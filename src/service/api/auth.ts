import { request } from '../request'

/**
 * 用户登录接口
 * @param userName 用户名
 * @param password 密码
 * @returns 返回登录token信息
 */
export function fetchLogin(userName: string, password: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/login',
    method: 'post',
    data: {
      userName,
      password,
    },
  })
}

/**
 * 获取用户信息接口
 * @returns 返回用户信息
 */
export function fetchGetUserInfo() {
  return request<Api.Auth.UserInfo>({ url: '/auth/getUserInfo' })
}

/**
 * 刷新token接口
 * @param refreshToken 刷新token
 * @returns 返回新的token信息
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/refreshToken',
    method: 'post',
    data: {
      refreshToken,
    },
  })
}

/**
 * 模拟后端错误接口
 * @param code 错误码
 * @param msg 错误信息
 * @returns 返回错误响应
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: '/auth/error', params: { code, msg } })
}
