import type { AxiosResponse } from 'axios'
import { BACKEND_ERROR_CODE, createFlatRequest, createRequest } from '@sa/axios'
import { createCancelTask, getAuthorization, handleExpiredRequest, showErrorMsg } from './shared'
import type { RequestInstanceState } from './type'
import { useAuthStore } from '@/store/modules/auth'
import { localStg } from '@/utils/storage'
import { getServiceBaseURL } from '@/utils/service'

// 判断是否使用代理
const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y'
// 获取服务基础URL
const { baseURL, otherBaseURL } = getServiceBaseURL(import.meta.env, isHttpProxy)

/**
 * 主请求实例
 * @description 封装了请求拦截、响应处理和错误处理逻辑
 */
export const request = createFlatRequest<App.Service.Response, RequestInstanceState>(
  {
    baseURL,
    headers: {
      apifoxToken: 'XL299LiMEDZ0H5h3A29PxwQXdMJqWyY2', // API测试工具token
    },
  },
  {
    /**
     * 请求拦截器
     * @param config 请求配置对象
     * @returns 处理后的请求配置
     * @description 在请求发送前添加认证头信息
     * 1. 调用getAuthorization获取认证token
     * 2. 将token添加到请求头中
     * 3. 返回修改后的请求配置
     */
    async onRequest(config) {
      const Authorization = getAuthorization()
      Object.assign(config.headers, { Authorization }) // 添加认证头

      return config
    },

    /**
     * 判断请求是否成功
     * @param response 响应数据
     * @returns 是否成功
     */
    isBackendSuccess(response) {
      return String(response.data.code) === import.meta.env.VITE_SERVICE_SUCCESS_CODE
    },

    /**
     * 处理后端请求失败逻辑
     * @param response 失败的响应对象
     * @param instance 请求实例
     * @returns 返回null或重新请求的Promise
     * @description 处理不同类型的后端错误：
     * 1. 需要直接登出的错误码
     * 2. 需要弹窗提示后登出的错误码
     * 3. token过期的错误码(尝试刷新token)
     */
    async onBackendFail(response, instance) {
      const authStore = useAuthStore()
      const responseCode = String(response.data.code)

      // 登出处理函数
      function handleLogout() {
        authStore.resetStore()
      }

      // 登出并清理相关资源
      function logoutAndCleanup() {
        handleLogout()
        window.removeEventListener('beforeunload', handleLogout)
        request.state.errMsgStack = request.state.errMsgStack.filter(msg => msg !== response.data.msg)
      }

      // 处理需要直接登出的错误码
      const logoutCodes = import.meta.env.VITE_SERVICE_LOGOUT_CODES?.split(',') || []
      if (logoutCodes.includes(responseCode)) {
        handleLogout()
        return null
      }

      // 处理需要弹窗提示后登出的错误码
      const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || []
      if (modalLogoutCodes.includes(responseCode) && !request.state.errMsgStack?.includes(response.data.msg)) {
        request.state.errMsgStack = [...(request.state.errMsgStack || []), response.data.msg]

        window.addEventListener('beforeunload', handleLogout)

        window.$dialog?.error({
          title: '错误',
          content: response.data.msg,
          positiveText: '确认',
          maskClosable: false,
          closeOnEsc: false,
          onPositiveClick() {
            logoutAndCleanup()
          },
          onClose() {
            logoutAndCleanup()
          },
        })

        return null
      }

      // 处理token过期情况，尝试刷新token
      const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || []
      if (expiredTokenCodes.includes(responseCode)) {
        const success = await handleExpiredRequest(request.state)
        if (success) {
          const Authorization = getAuthorization()
          Object.assign(response.config.headers, { Authorization })

          return instance.request(response.config) as Promise<AxiosResponse>
        }
      }

      return null
    },

    /**
     * 转换后端响应数据
     * @param response 原始响应对象
     * @returns 返回处理后的业务数据部分
     * @description 从响应对象中提取业务数据(data字段)返回
     */
    transformBackendResponse(response) {
      return response.data.data
    },

    /**
     * 请求错误处理回调
     * @param error 错误对象
     * @description 处理请求过程中发生的错误，包括：
     * 1. 提取错误消息和错误码
     * 2. 处理需要弹窗登出的错误码
     * 3. 处理token过期的错误码
     * 4. 显示其他错误消息
     */
    onError(error) {
      let message = error.message
      let backendErrorCode = ''

      // 如果是后端返回的错误，提取错误消息和错误码
      if (error.code === BACKEND_ERROR_CODE) {
        message = error.response?.data?.msg || message
        backendErrorCode = String(error.response?.data?.code || '')
      }

      // 处理需要弹窗登出的错误码
      const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || []
      if (modalLogoutCodes.includes(backendErrorCode)) {
        return
      }

      // 处理token过期的错误码
      const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || []
      if (expiredTokenCodes.includes(backendErrorCode)) {
        return
      }

      // 显示其他错误消息
      showErrorMsg(request.state, message)
    },
  },
)

/**
 * 示例请求实例
 * @description 用于演示不同配置的请求实例
 */
export const demoRequest = createRequest<App.Service.DemoResponse>(
  {
    baseURL: otherBaseURL.demo,
  },
  {
    /**
     * 请求拦截器
     * @param config 请求配置对象
     * @returns 处理后的请求配置
     * @description 在请求发送前添加认证头信息
     * 1. 从本地存储获取token
     * 2. 如果存在token则添加Bearer认证头
     * 3. 返回修改后的请求配置
     */
    async onRequest(config) {
      const { headers } = config
      const token = localStg.get('token')
      const Authorization = token ? `Bearer ${token}` : null
      Object.assign(headers, { Authorization })

      return config
    },

    /**
     * 判断请求是否成功
     * @param response 响应数据对象
     * @returns 返回布尔值表示请求是否成功
     * @description 通过检查响应状态码是否为'200'来判断请求是否成功
     */
    isBackendSuccess(response) {
      return response.data.status === '200'
    },

    /**
     * 后端返回失败处理回调
     * @param _response 失败的响应对象
     * @description 当后端返回非成功状态码时触发，可用于处理token刷新等逻辑
     */
    async onBackendFail(_response) {
      // 可在此处处理刷新token等逻辑
    },

    /**
     * 转换后端响应数据
     * @param response 原始响应对象
     * @returns 返回处理后的业务数据部分
     * @description 从响应对象中提取业务数据(result字段)返回
     */
    transformBackendResponse(response) {
      return response.data.result
    },

    /**
     * 请求错误处理回调
     * @param error 错误对象
     * @description 处理请求过程中发生的错误，显示错误提示
     */
    onError(error) {
      let message = error.message

      // 如果是后端返回的错误，使用后端错误消息
      if (error.code === BACKEND_ERROR_CODE) {
        message = error.response?.data?.message || message
      }

      // 使用Naive UI的消息组件显示错误
      window.$message?.error(message)
    },
  },
)

// 导出取消请求相关方法
export {
  createCancelTask,
}
