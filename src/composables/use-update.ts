import { onMounted, onUnmounted, ref } from 'vue'
import { useMessage } from 'naive-ui'
import { UpdateStatus, electronUpdateAPI, isElectronEnvironment } from '@/utils/electron-update'
import type { UpdateInfo } from '@/utils/electron-update'

/**
 * 更新管理 Composable
 * 提供应用更新的状态管理和操作方法
 */
export function useUpdate() {
  const message = useMessage()

  // 响应式状态
  const updateInfo = ref<UpdateInfo | null>(null)
  const currentVersion = ref<string>('')
  const showUpdateDialog = ref(false)
  const isChecking = ref(false)
  const isDownloading = ref(false)
  const isInstalling = ref(false)

  // 更新状态监听器的清理函数
  let removeUpdateListener: (() => void) | null = null

  /**
   * 处理更新状态变化
   */
  const handleUpdateStatus = (info: UpdateInfo) => {
    updateInfo.value = info

    // 更新各种状态标志
    isChecking.value = info.status === UpdateStatus.CHECKING
    isDownloading.value = info.status === UpdateStatus.DOWNLOADING
    isInstalling.value = info.status === UpdateStatus.INSTALLING

    // 根据状态显示相应的消息
    switch (info.status) {
      case UpdateStatus.CHECKING:
        // 静默检查时不显示消息
        break

      case UpdateStatus.AVAILABLE:
        message.info(`发现新版本 ${info.version}，点击查看详情`)
        showUpdateDialog.value = true
        break

      case UpdateStatus.NOT_AVAILABLE:
        // 只在手动检查时显示
        if (!isChecking.value) {
          message.success('当前已是最新版本')
        }
        break

      case UpdateStatus.DOWNLOADED:
        message.success('更新下载完成，可以安装了')
        showUpdateDialog.value = true
        break

      case UpdateStatus.ERROR:
        message.error(`更新失败: ${info.error}`)
        showUpdateDialog.value = true
        break

      case UpdateStatus.INSTALLING:
        message.info('正在安装更新，应用即将重启...')
        break
    }
  }

  /**
   * 初始化更新管理
   */
  const initialize = async () => {
    if (!isElectronEnvironment()) {
      console.warn('不在 Electron 环境中，跳过更新功能初始化')
      return
    }

    try {
      // 获取当前版本
      const version = await electronUpdateAPI.getCurrentVersion()
      if (version) {
        currentVersion.value = version
      }

      // 设置更新状态监听器
      removeUpdateListener = electronUpdateAPI.onUpdateStatus(handleUpdateStatus)

      // 获取当前更新信息
      const info = await electronUpdateAPI.getUpdateInfo()
      if (info) {
        updateInfo.value = info
      }
    }
    catch (error) {
      console.error('初始化更新管理失败:', error)
    }
  }

  /**
   * 手动检查更新
   */
  const checkForUpdates = async (silent = false) => {
    if (!isElectronEnvironment()) {
      message.warning('不在 Electron 环境中')
      return
    }

    if (isChecking.value) {
      message.warning('正在检查更新中，请稍候')
      return
    }

    try {
      const result = await electronUpdateAPI.checkForUpdates(silent)

      if (!result.success) {
        message.error(`检查更新失败: ${result.error}`)
      }
      else if (!silent) {
        message.info('正在检查更新...')
      }
    }
    catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      message.error(`检查更新失败: ${errorMessage}`)
    }
  }

  /**
   * 下载更新
   */
  const downloadUpdate = async () => {
    if (!isElectronEnvironment()) {
      message.warning('不在 Electron 环境中')
      return
    }

    if (isDownloading.value) {
      message.warning('正在下载更新中')
      return
    }

    try {
      const result = await electronUpdateAPI.downloadUpdate()

      if (!result.success) {
        message.error(`下载更新失败: ${result.error}`)
      }
      else {
        message.info('开始下载更新...')
      }
    }
    catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      message.error(`下载更新失败: ${errorMessage}`)
    }
  }

  /**
   * 安装更新
   */
  const installUpdate = async () => {
    if (!isElectronEnvironment()) {
      message.warning('不在 Electron 环境中')
      return
    }

    if (isInstalling.value) {
      message.warning('正在安装更新中')
      return
    }

    try {
      const result = await electronUpdateAPI.installUpdate()

      if (!result.success) {
        message.error(`安装更新失败: ${result.error}`)
      }
      // 成功的话应用会重启，不需要显示消息
    }
    catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      message.error(`安装更新失败: ${errorMessage}`)
    }
  }

  /**
   * 设置更新服务器地址
   */
  const setUpdateServerUrl = async (url: string) => {
    if (!isElectronEnvironment()) {
      message.warning('不在 Electron 环境中')
      return false
    }

    try {
      const result = await electronUpdateAPI.setUpdateServerUrl(url)

      if (result.success) {
        message.success('更新服务器地址设置成功')
        return true
      }
      else {
        message.error(`设置更新服务器失败: ${result.error}`)
        return false
      }
    }
    catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      message.error(`设置更新服务器失败: ${errorMessage}`)
      return false
    }
  }

  /**
   * 关闭更新对话框
   */
  const closeUpdateDialog = () => {
    showUpdateDialog.value = false
  }

  /**
   * 处理更新对话框的下载按钮点击
   */
  const handleDialogDownload = () => {
    downloadUpdate()
  }

  /**
   * 处理更新对话框的安装按钮点击
   */
  const handleDialogInstall = () => {
    installUpdate()
    closeUpdateDialog()
  }

  /**
   * 处理更新对话框的取消按钮点击
   */
  const handleDialogCancel = () => {
    closeUpdateDialog()
  }

  /**
   * 获取更新状态文本
   */
  const getUpdateStatusText = () => {
    if (!updateInfo.value)
      return '未知'

    switch (updateInfo.value.status) {
      case UpdateStatus.CHECKING:
        return '正在检查更新...'
      case UpdateStatus.AVAILABLE:
        return `发现新版本 ${updateInfo.value.version}`
      case UpdateStatus.NOT_AVAILABLE:
        return '已是最新版本'
      case UpdateStatus.DOWNLOADING:
        return `正在下载更新... ${updateInfo.value.progress ? `${Math.round(updateInfo.value.progress.percent)}%` : ''}`
      case UpdateStatus.DOWNLOADED:
        return '更新下载完成'
      case UpdateStatus.ERROR:
        return `更新出错: ${updateInfo.value.error}`
      case UpdateStatus.INSTALLING:
        return '正在安装更新...'
      default:
        return '未知状态'
    }
  }

  // 生命周期钩子
  onMounted(() => {
    initialize()
  })

  onUnmounted(() => {
    // 清理更新状态监听器
    if (removeUpdateListener) {
      removeUpdateListener()
      removeUpdateListener = null
    }
  })

  return {
    // 状态
    updateInfo,
    currentVersion,
    showUpdateDialog,
    isChecking,
    isDownloading,
    isInstalling,

    // 方法
    checkForUpdates,
    downloadUpdate,
    installUpdate,
    setUpdateServerUrl,
    closeUpdateDialog,
    handleDialogDownload,
    handleDialogInstall,
    handleDialogCancel,
    getUpdateStatusText,

    // 工具方法
    isElectronEnvironment: () => isElectronEnvironment(),
  }
}
