/**
 * 获取本地SVG图标文件名列表（不带.svg后缀）
 * @returns {string[]} 返回src/assets/svg-icon/目录下所有.svg文件的文件名数组
 */
export function getLocalIcons() {
  // 使用vite的import.meta.glob获取所有svg文件
  const svgIcons = import.meta.glob('/src/assets/svg-icon/*.svg')

  // 处理获取到的文件路径：
  // 1. 获取所有键名（文件路径）
  // 2. 提取文件名（最后一个路径部分）
  // 3. 移除.svg后缀
  // 4. 过滤掉空值
  const keys = Object.keys(svgIcons)
    .map(item => item.split('/').at(-1)?.replace('.svg', '') || '')
    .filter(Boolean)

  return keys
}
