/**
 * 将键值对记录转换为选项数组
 * @example
 *   ```ts
 *   const record = {
 *     key1: 'label1',
 *     key2: 'label2'
 *   };
 *   const options = transformRecordToOption(record);
 *   // [
 *   //   { value: 'key1', label: 'label1' },
 *   //   { value: 'key2', label: 'label2' }
 *   // ]
 *   ```;
 * @param record 键值对记录对象
 * @returns 转换后的选项数组，每个元素包含value和label属性
 */
export function transformRecordToOption<T extends Record<string, string>>(record: T) {
  return Object.entries(record).map(([value, label]) => ({
    value,
    label,
  })) as CommonType.Option<keyof T>[]
}

/**
 * 翻译选项数组（当前实现为直接返回，可扩展为实际翻译逻辑）
 * @param options 原始选项数组
 * @returns 翻译后的选项数组
 */
export function translateOptions(options: CommonType.Option<string>[]) {
  // 当前实现为直接返回原选项，可在此处添加实际翻译逻辑
  return options.map(option => ({
    ...option,
  }))
}

/**
 * 切换HTML根元素的类名
 * @param className 要切换的类名
 * @returns 包含add和remove方法的对象
 */
export function toggleHtmlClass(className: string) {
  // 添加类名的方法
  function add() {
    document.documentElement.classList.add(className)
  }

  // 移除类名的方法
  function remove() {
    document.documentElement.classList.remove(className)
  }

  return {
    add,
    remove,
  }
}
