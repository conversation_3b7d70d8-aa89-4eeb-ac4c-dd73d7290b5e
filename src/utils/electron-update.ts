/**
 * Electron 更新相关的工具函数
 * 提供渲染进程与主进程的更新通信接口
 */

import type { UpdateInfo, UpdateStatus } from '../../electron/main/services/update-service'

/**
 * 更新API类
 */
export class ElectronUpdateAPI {
  private static instance: ElectronUpdateAPI | null = null
  private updateStatusCallbacks: Array<(info: UpdateInfo) => void> = []

  private constructor() {
    this.setupUpdateListener()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ElectronUpdateAPI {
    if (!ElectronUpdateAPI.instance) {
      ElectronUpdateAPI.instance = new ElectronUpdateAPI()
    }
    return ElectronUpdateAPI.instance
  }

  /**
   * 设置更新状态监听器
   */
  private setupUpdateListener(): void {
    if (window.optimizedIPC) {
      window.optimizedIPC.on('update-status', (info: UpdateInfo) => {
        this.updateStatusCallbacks.forEach(callback => callback(info))
      })
    }
  }

  /**
   * 检查更新
   * @param silent 是否静默检查
   */
  async checkForUpdates(silent = false): Promise<{ success: boolean, error?: string }> {
    if (!window.optimizedIPC) {
      return { success: false, error: '不在 Electron 环境中' }
    }

    try {
      return await window.optimizedIPC.invoke('update:check', silent)
    }
    catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      }
    }
  }

  /**
   * 下载更新
   */
  async downloadUpdate(): Promise<{ success: boolean, error?: string }> {
    if (!window.optimizedIPC) {
      return { success: false, error: '不在 Electron 环境中' }
    }

    try {
      return await window.optimizedIPC.invoke('update:download')
    }
    catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      }
    }
  }

  /**
   * 安装更新
   */
  async installUpdate(): Promise<{ success: boolean, error?: string }> {
    if (!window.optimizedIPC) {
      return { success: false, error: '不在 Electron 环境中' }
    }

    try {
      return await window.optimizedIPC.invoke('update:install')
    }
    catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      }
    }
  }

  /**
   * 获取更新信息
   */
  async getUpdateInfo(): Promise<UpdateInfo | null> {
    if (!window.optimizedIPC) {
      return null
    }

    try {
      return await window.optimizedIPC.invoke('update:getInfo')
    }
    catch (error) {
      console.error('获取更新信息失败:', error)
      return null
    }
  }

  /**
   * 获取当前版本
   */
  async getCurrentVersion(): Promise<string | null> {
    if (!window.optimizedIPC) {
      return null
    }

    try {
      return await window.optimizedIPC.invoke('update:getCurrentVersion')
    }
    catch (error) {
      console.error('获取当前版本失败:', error)
      return null
    }
  }

  /**
   * 设置更新服务器地址
   */
  async setUpdateServerUrl(url: string): Promise<{ success: boolean, error?: string }> {
    if (!window.optimizedIPC) {
      return { success: false, error: '不在 Electron 环境中' }
    }

    try {
      return await window.optimizedIPC.invoke('update:setServerUrl', url)
    }
    catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      }
    }
  }

  /**
   * 添加更新状态监听器
   */
  onUpdateStatus(callback: (info: UpdateInfo) => void): () => void {
    this.updateStatusCallbacks.push(callback)

    // 返回取消监听的函数
    return () => {
      const index = this.updateStatusCallbacks.indexOf(callback)
      if (index > -1) {
        this.updateStatusCallbacks.splice(index, 1)
      }
    }
  }

  /**
   * 移除所有监听器
   */
  removeAllListeners(): void {
    this.updateStatusCallbacks = []
  }
}

/**
 * 更新状态枚举（重新导出）
 */
export { UpdateStatus } from '../../electron/main/services/update-service'

/**
 * 更新信息接口（重新导出）
 */
export type { UpdateInfo } from '../../electron/main/services/update-service'

/**
 * 导出单例实例
 */
export const electronUpdateAPI = ElectronUpdateAPI.getInstance()

/**
 * 格式化文件大小
 */
export function formatBytes(bytes: number): string {
  if (bytes === 0)
    return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`
}

/**
 * 格式化下载速度
 */
export function formatSpeed(bytesPerSecond: number): string {
  return `${formatBytes(bytesPerSecond)}/s`
}

/**
 * 格式化更新状态文本
 */
export function formatUpdateStatus(status: UpdateStatus): string {
  const statusMap: Record<UpdateStatus, string> = {
    [UpdateStatus.CHECKING]: '正在检查更新...',
    [UpdateStatus.AVAILABLE]: '发现新版本',
    [UpdateStatus.NOT_AVAILABLE]: '已是最新版本',
    [UpdateStatus.DOWNLOADING]: '正在下载更新...',
    [UpdateStatus.DOWNLOADED]: '更新下载完成',
    [UpdateStatus.ERROR]: '更新出错',
    [UpdateStatus.INSTALLING]: '正在安装更新...',
  }

  return statusMap[status] || '未知状态'
}

/**
 * 检查是否在 Electron 环境中
 */
export function isElectronEnvironment(): boolean {
  return typeof window !== 'undefined' && !!window.optimizedIPC
}
