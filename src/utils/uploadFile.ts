import OBS from 'esdk-obs-browserjs'

const { VITE_OBS_ACCESS_KEY_ID, VITE_OBS_SECRET_ACCESS_KEY } = import.meta.env
/**
 * OBS上传器配置选项
 */
interface OBSUploaderConfig {
  /** OBS服务端点 */
  endpoint: string
  /** 存储桶名称 */
  bucket: string
  /** 安全令牌(可选) */
  securityToken?: string
}

/**
 * 文件上传选项
 */
interface FileUploadOptions {
  /** 对象存储路径 */
  objectKey: string
  /** 分片大小(字节)，默认5MB */
  chunkSize?: number
}

/**
 * 分片信息
 */
interface PartInfo {
  /** 分片编号(从1开始) */
  partNumber: number
  /** 分片ETag标识 */
  etag: string
}

/**
 * 上传进度回调函数
 */
type ProgressCallback = (
  /** 上传进度百分比 */
  progress: number,
  /** 已上传字节数 */
  uploadedSize: number,
  /** 文件总字节数 */
  totalSize: number
) => void

/**
 * OBS大文件上传器
 */
export class OBSUploader {
  private client: any
  private config: OBSUploaderConfig

  /**
   * 创建OBS上传器实例
   * @param config - OBS配置
   */
  constructor(config: OBSUploaderConfig) {
    this.config = config
    this.client = new OBS({
      region: config.endpoint,
      accessKeyId: VITE_OBS_ACCESS_KEY_ID,
      accessKeySecret: VITE_OBS_SECRET_ACCESS_KEY,
      bucket: config.bucket,
      stsToken: config.securityToken,
    })
  }

  /**
   * 执行大文件分片上传
   * @param file - 要上传的文件
   * @param options - 上传选项
   * @param onProgress - 进度回调(可选)
   * @returns 返回上传后的文件URL
   * @throws 当上传失败时抛出错误
   */
  public async uploadLargeFile(
    file: File | Blob,
    options: FileUploadOptions,
    onProgress?: ProgressCallback,
  ): Promise<string> {
    const chunkSize = options.chunkSize || 5 * 1024 * 1024
    const fileSize = file.size
    const chunkCount = Math.ceil(fileSize / chunkSize)
    let uploadedSize = 0

    try {
      // 初始化分片上传
      const { uploadId } = await this.client.initMultipartUpload(options.objectKey)

      // 上传所有分片
      const partInfos: PartInfo[] = []
      for (let i = 0; i < chunkCount; i++) {
        const start = i * chunkSize
        const end = Math.min(start + chunkSize, fileSize)
        const chunk = file.slice(start, end)

        const result = await this.client.uploadPart(
          options.objectKey,
          uploadId,
          i + 1,
          chunk,
        )

        partInfos.push({
          partNumber: i + 1,
          etag: result.etag,
        })

        // 更新进度
        uploadedSize += end - start
        const progress = Math.round((uploadedSize / fileSize) * 100)
        onProgress?.(progress, uploadedSize, fileSize)
      }

      // 完成分片上传
      const result = await this.client.completeMultipartUpload(
        options.objectKey,
        uploadId,
        partInfos,
      )

      return result.url
    }
    catch (error) {
      throw new Error(`文件上传失败: ${(error as Error).message}`)
    }
  }
}
export default OBS
