<script setup lang="ts">
import { getScreenshot } from '@sa/utils'
import { WindowService } from '~/electron/render'

async function testHandleClick() {
  console.log('开始创建基础窗口...')

  try {
    const res = await WindowService.createWindow({
      width: 800,
      height: 600,
      minWidth: 400,
      minHeight: 300,
      resizable: true,
      frame: true,
      title: '测试窗口',
      route: '/home', // 指定要显示的路由
    })

    console.log('窗口创建结果:', res)

    if (res.success) {
      console.log(`✅ 窗口创建成功！窗口ID: ${res.data?.windowId}`)
    }
    else {
      console.error('❌ 窗口创建失败:', res.error)
    }
  }
  catch (error) {
    console.error('❌ 创建窗口时发生异常:', error)
  }
}

async function createSettingsWindow() {
  console.log('创建设置窗口...')

  try {
    const res = await WindowService.createSettingsWindow()

    if (res.success) {
      console.log(`✅ 设置窗口创建成功！窗口ID: ${res.data?.windowId}`)
    }
    else {
      console.error('❌ 设置窗口创建失败:', res.error)
    }
  }
  catch (error) {
    console.error('❌ 创建设置窗口时发生异常:', error)
  }
}

async function createCustomRouteWindow() {
  console.log('创建自定义路由窗口...')

  try {
    const res = await WindowService.createWindow({
      width: 900,
      height: 700,
      title: '用户管理',
      route: '/manage/dept', // 显示部门管理页面
    })

    if (res.success) {
      console.log(`✅ 自定义路由窗口创建成功！窗口ID: ${res.data?.windowId}`)
    }
    else {
      console.error('❌ 自定义路由窗口创建失败:', res.error)
    }
  }
  catch (error) {
    console.error('❌ 创建自定义路由窗口时发生异常:', error)
  }
}

// ==================== 模态窗口示例 ====================

async function createModalWindow() {
  console.log('创建模态窗口...')

  try {
    // 假设主窗口ID为1，实际使用中可以通过其他方式获取
    const parentWindowId = 1

    const res = await WindowService.createModalWindow(
      parentWindowId,
      '/manage/user-detail/1', // 显示用户详情页面
      {
        title: '用户详情 - 模态窗口',
        width: 700,
        height: 500,
        resizable: false, // 模态窗口通常不允许调整大小
      },
    )

    if (res.success && res.data?.windowId) {
      console.log(`✅ 模态窗口创建成功！窗口ID: ${res.data.windowId}`)
    }
    else {
      console.error('❌ 模态窗口创建失败:', res.error)
    }
  }
  catch (error) {
    console.error('❌ 创建模态窗口时发生异常:', error)
  }
}

async function createConfirmDialog() {
  console.log('创建确认对话框...')

  try {
    const parentWindowId = 1

    const res = await WindowService.createModalWindow(
      parentWindowId,
      '/modal-dialog', // 使用专门的对话框页面
      {
        title: '确认操作',
        width: 500,
        height: 300,
        resizable: false,
        minimizable: false,
        maximizable: false,
      },
    )

    if (res.success && res.data?.windowId) {
      console.log(`✅ 确认对话框创建成功！窗口ID: ${res.data.windowId}`)
    }
    else {
      console.error('❌ 确认对话框创建失败:', res.error)
    }
  }
  catch (error) {
    console.error('❌ 创建确认对话框时发生异常:', error)
  }
}

async function createSettingsModal() {
  console.log('创建设置模态窗口...')

  try {
    const parentWindowId = 1

    const res = await WindowService.createModalWindow(
      parentWindowId,
      '/manage/menu', // 显示菜单管理页面作为设置示例
      {
        title: '应用设置',
        width: 800,
        height: 600,
        resizable: true,
      },
    )

    if (res.success && res.data?.windowId) {
      console.log(`✅ 设置模态窗口创建成功！窗口ID: ${res.data.windowId}`)
    }
    else {
      console.error('❌ 设置模态窗口创建失败:', res.error)
    }
  }
  catch (error) {
    console.error('❌ 创建设置模态窗口时发生异常:', error)
  }
}
</script>

<template>
  <div>
    <NCard>
      <!-- 调试信息面板 -->
      <div class="mb-4 rounded bg-gray-100 p-4">
        <NButton @click="getScreenshot">
          截图
        </NButton>
      </div>

      <iframe src="https://tch.eduwon.cn/Home/Introduce" width="1000px" height="400px" />
      <div class="mb-4 flex flex-wrap gap-4">
        <NButton @click="testHandleClick">
          创建基础窗口
        </NButton>
        <NButton @click="createSettingsWindow">
          创建设置窗口
        </NButton>

        <NButton @click="createCustomRouteWindow">
          创建部门管理窗口
        </NButton>
      </div>
      <div class="mb-4 flex flex-wrap gap-4">
        <NButton @click="createModalWindow">
          创建模态窗口
        </NButton>
        <NButton @click="createConfirmDialog">
          创建确认对话框
        </NButton>
        <NButton @click="createSettingsModal">
          创建设置模态窗口
        </NButton>
      </div>

      <li v-for="(_, index) in 200" :key="index">
        {{ index }}
      </li>
    </NCard>
  </div>
</template>

<style scoped></style>
