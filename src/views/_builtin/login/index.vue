<script setup lang="ts">
import { computed } from 'vue'
import type { Component } from 'vue'
import { getPaletteColorByNumber, mixColor } from '@sa/color'
import PwdLogin from './modules/pwd-login.vue'
import CodeLogin from './modules/code-login.vue'
import Register from './modules/register.vue'
import ResetPwd from './modules/reset-pwd.vue'
import { useThemeStore } from '@/store/modules/theme'

interface Props {
  /** The login module */
  module?: UnionKey.LoginModule
}

const props = defineProps<Props>()

const themeStore = useThemeStore()

interface LoginModule {
  label: string
  component: Component
}

const moduleMap: Record<UnionKey.LoginModule, LoginModule> = {
  'pwd-login': { label: '密码登录', component: PwdLogin },
  'code-login': { label: '验证码登录', component: CodeLogin },
  'register': { label: '注册账号', component: Register },
  'reset-pwd': { label: '重置密码', component: ResetPwd },
}

const activeModule = computed(() => moduleMap[props.module || 'pwd-login'])

const bgThemeColor = computed(() =>
  themeStore.darkMode ? getPaletteColorByNumber(themeStore.themeColor, 600) : themeStore.themeColor,
)

const bgColor = computed(() => {
  const COLOR_WHITE = '#ffffff'

  const ratio = themeStore.darkMode ? 0.5 : 0.2

  return mixColor(COLOR_WHITE, themeStore.themeColor, ratio)
})
const title = import.meta.env.VITE_APP_TITLE
</script>

<template>
  <div class="relative size-full flex-center overflow-hidden" :style="{ backgroundColor: bgColor }">
    <WaveBg :theme-color="bgThemeColor" />
    <div class="draggable position-absolute left-0 top-0 z-9999 h-[35px] w-full bg-transparent" />
    <NCard :bordered="false" class="relative z-4 w-auto rd-12px">
      <div class="w-400px lt-sm:w-300px">
        <header class="flex-y-center justify-between">
          <SystemLogo class="text-64px text-primary lt-sm:text-48px" />
          <h3 class="text-28px text-primary font-500 lt-sm:text-22px">
            {{ title }}
          </h3>
          <div class="i-flex-col">
            <ThemeSchemaSwitch
              :theme-schema="themeStore.themeScheme"
              :show-tooltip="false"
              class="text-20px lt-sm:text-18px"
              @switch="themeStore.toggleThemeScheme"
            />
          </div>
        </header>
        <main class="pt-24px">
          <h3 class="text-18px text-primary font-medium">
            {{ activeModule.label }}
          </h3>
          <div class="pt-24px">
            <Transition :name="themeStore.page.animateMode" mode="out-in" appear>
              <component :is="activeModule.component" />
            </Transition>
          </div>
        </main>
      </div>
    </NCard>
  </div>
</template>

<style scoped lang="scss">
.draggable{
  -webkit-app-region: drag;
}
</style>
