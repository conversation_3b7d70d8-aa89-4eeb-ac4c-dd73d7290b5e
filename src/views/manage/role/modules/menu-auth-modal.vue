<script setup lang="ts">
import { computed, shallowRef, watch } from 'vue'
import { fetchGetAllPages, fetchGetMenuTree } from '@/service/api'

defineOptions({
  name: 'MenuAuthModal',
})

const props = defineProps<Props>()

interface Props {
  /** the roleId */
  roleId: number
}

const visible = defineModel<boolean>('visible', {
  default: false,
})

function closeModal() {
  visible.value = false
}

const home = shallowRef('')

async function getHome() {
  console.log(props.roleId)

  home.value = 'home'
}

async function updateHome(val: string) {
  // request

  home.value = val
}

const pages = shallowRef<string[]>([])

async function getPages() {
  const { error, data } = await fetchGetAllPages()

  if (!error) {
    pages.value = data
  }
}

const pageSelectOptions = computed(() => {
  const opts: CommonType.Option[] = pages.value.map(page => ({
    label: page,
    value: page,
  }))

  return opts
})

const tree = shallowRef<Api.SystemManage.MenuTree[]>([])

async function getTree() {
  const { error, data } = await fetchGetMenuTree()

  if (!error) {
    tree.value = data
  }
}

const checks = shallowRef<number[]>([])

async function getChecks() {
  console.log(props.roleId)
  // request
  checks.value = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]
}

function handleSubmit() {
  console.log(checks.value, props.roleId)
  // request

  window.$message?.success?.('修改成功')

  closeModal()
}

function init() {
  getHome()
  getPages()
  getTree()
  getChecks()
}

watch(visible, (val) => {
  if (val) {
    init()
  }
})
</script>

<template>
  <NModal v-model:show="visible" title="编辑菜单权限" preset="card" class="w-480px">
    <div class="flex-y-center gap-16px pb-12px">
      <div>首页</div>
      <NSelect
        :value="home"
        :options="pageSelectOptions"
        size="small"
        class="w-160px"
        @update:value="updateHome"
      />
    </div>
    <NTree
      v-model:checked-keys="checks"
      :data="tree"
      key-field="id"
      checkable
      expand-on-click
      virtual-scroll
      block-line
      class="h-280px"
    />
    <template #footer>
      <NSpace justify="end">
        <NButton size="small" class="mt-16px" @click="closeModal">
          取消
        </NButton>
        <NButton type="primary" size="small" class="mt-16px" @click="handleSubmit">
          确认
        </NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
