<script setup lang="ts">
import FabricDemo from './FabricDemo.vue'

import FromView from '@/components/common/from/src/index.vue'
import type { FormOptions } from '@/components/common/from/src/types/types'

const options: FormOptions[] = [
  {
    type: 'input',
    label: '用户名',
    value: 'admin',
    path: 'username', // 表单路径，用于表单提交时获取对应的值
    rules: [
      {
        required: true,
        message: '请输入用户名名称',
        trigger: 'blur',
      },
      {
        min: 2,
        max: 10,
        message: '长度在 2 到 10 个字符',
        trigger: ['input', 'blur'],
      },
    ],
    attrs: {
      clearable: true,
    },
  },
  {
    type: 'checkboxGroup',
    label: '复选框',
    value: [],
    path: 'checkboxGroupValue',
    children: [
      {
        type: 'checkbox',
        label: '超级管理员',
        value: 'super_admin',
      },
      {
        type: 'checkbox',
        label: '管理员',
        value: 'admin',
      },
      {
        type: 'checkbox',
        label: '普通用户',
        value: 'user',
      },
    ],
    rules: [
      {
        type: 'array',
        required: true,
        trigger: 'change',
        message: '请选择 checkboxGroupValue',
      },
    ],
  },
  {
    type: 'radioGroup',
    label: '单选框',
    value: '',
    path: 'radioGroupValue',
    children: [
      {
        type: 'radio',
        label: '小',
        value: '1',
      },
      {
        type: 'radio',
        label: '中',
        value: '2',
      },
      {
        type: 'radio',
        label: '大',
        value: '3',
      },
    ],
    rules: [
      {
        required: true,
        message: '请选择 radioGroupValue',
        trigger: ['change'],
      },
    ],
  },

  {
    type: 'radioGroup',
    label: '单选框按钮',
    value: '',
    path: 'radioButtonGroupValue',
    children: [
      {
        type: 'radioButton',
        label: '小',
        value: '1',
      },
      {
        type: 'radioButton',
        label: '中',
        value: '2',
      },
      {
        type: 'radioButton',
        label: '大',
        value: '3',
      },
    ],
    rules: [
      {
        required: true,
        message: '请选择 radioButtonGroupValue',
        trigger: ['change'],
      },
    ],
  },

  {
    type: 'input',
    label: '密码',
    value: '123456',
    path: 'password',
    rules: [
      {
        required: true,
        message: '请输入密码',
        trigger: 'blur',
      },
      {
        min: 6,
        max: 15,
        message: '长度在 6 到 15 个字符',
        trigger: ['input', 'blur'],
      },
    ],
    attrs: {
      clearable: true,
      type: 'password',
      showPasswordOn: 'click',
    },
  },
  {
    type: 'input',
    label: '多行文本',
    placeholder: '请输入多行文本',
    value: '',
    path: 'textarea',
    rules: [
      {
        required: true,
        message: '请输入多行文本',
        trigger: 'blur',
      },
      {
        min: 6,
        max: 15,
        message: '长度在 26 到 35 个字符',
        trigger: ['input', 'blur'],
      },
    ],
    attrs: {
      clearable: true,
      type: 'textarea',
      showPasswordOn: 'click',
    },
  },
  {
    type: 'select',
    label: '单选角色',
    value: '',
    path: 'role',
    rules: [
      {
        required: true,
        message: '请选择角色',
        trigger: ['change', 'blur'],
      },
    ],
    attrs: {
      clearable: true,
      options: [
        {
          label: '超级管理员',
          value: 'super_admin',
        },
        {
          label: '管理员',
          value: 'admin',
        },
        {
          label: '普通用户',
          value: 'user',
        },
      ],
    },
  },
  {
    type: 'select',
    label: '多选角色',
    value: '',
    path: 'multipleSelectValue',
    rules: [
      {
        type: 'array',
        required: true,
        trigger: ['blur', 'change'],
        message: '请选择 multipleSelectValue',
      },
    ],
    attrs: {
      clearable: true,
      multiple: true,
      options: [
        {
          label: '超级管理员',
          value: 'super_admin',
        },
        {
          label: '管理员',
          value: 'admin',
        },
        {
          label: '普通用户',
          value: 'user',
        },
      ],
    },
  },
  {
    type: 'datePicker',
    label: '选择日期',
    value: null,
    path: 'datetimeValue',
    rules: [
      {
        type: 'number',
        required: true,
        trigger: ['blur', 'change'],
        message: '请输入 datetimeValue',
      },
    ],
    attrs: {
      clearable: true,
      type: 'datetime',
      placeholder: '请选择日期',
    },
  },
  {
    type: 'switch',
    label: 'Switch',
    value: false,
    path: 'switchValue',
  },
  {
    type: 'upload',
    label: '上传',
    value: [],
    path: 'uploadValue',
    uploadAttrs: {
      action: 'https://www.mocky.io/v2/5e4bafc63100007100d8b70f',
      headers: {
        'naive-info': 'hello!',
      },
      data: {
        'naive-data': 'cool! naive!',
      },
    },
  },
]
function onErrorHandler(e: any) {
  console.log(e)
}
function handleValidateButtonClick() {
  console.log('validate')
}
function resetFrom() {

}
</script>

<template>
  <div>
    <FabricDemo />
    <NCard :bordered="false" size="small" class="card-wrapper">
      <FromView :options="options" label-width="80px" label-placement="left" label-align="left" @on-error="onErrorHandler">
        <template #uploadArea>
          <NButton type="primary">
            提交
          </NButton>
        </template>
        <template #uploadTip>
          <div depth="3" style="margin: 8px 0 0 0">
            请不要上传敏感数据，比如你的银行卡号和密码，信用卡号有效期和安全码
          </div>
        </template>
        <template #footer>
          <n-button round type="primary" @click="resetFrom">
            重置
          </n-button>
          <n-button round type="primary" @click="handleValidateButtonClick">
            提交
          </n-button>
        </template>
      </FromView>
    </NCard>
  </div>
</template>
