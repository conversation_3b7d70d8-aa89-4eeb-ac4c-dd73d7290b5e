<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import FabricDrawingBoard from '@yw/fabric'

const fdb = ref<FabricDrawingBoard>()
const canvasReadonly = ref(false)

const myCanvasRef = ref<HTMLCanvasElement>()

onMounted(() => {
  fdb.value = new FabricDrawingBoard({ el: myCanvasRef.value })
})

function setBrushColor(color: string) {
  fdb.value?.setBrushColor(color)
}

function setStrokeColor(color: string) {
  fdb.value?.setStrokeColor(color)
}

function setFillColor(color: string) {
  fdb.value?.setFillColor(color)
}

function setBgColor(color: string) {
  fdb.value?.setBgColor(color)
}

function tapBrush(size: number) {
  fdb.value?.setBrushSize(size)
  fdb.value?.drawBrush()
}

function tapLine(size: number) {
  fdb.value?.setStrokeSize(size)
  fdb.value?.drawLine()
}

function tapRect(size: number | string) {
  fdb.value?.setStrokeSize(size)
  fdb.value?.drawRect()
}

function tapCircle(size: number) {
  fdb.value?.setStrokeSize(size)
  fdb.value?.drawCircle()
}

function tapText(size: number) {
  fdb.value?.setFontSize(size)
  fdb.value?.drawText()
}

function tapEraser(size: number) {
  fdb.value?.setEraserSize(size)
  fdb.value?.useEraser()
}

function tapMove() {
  fdb.value?.useMove()
}

function tapZoomDown() {
  fdb.value?.canvasZoomDown()
}

function tapZoomUp() {
  fdb.value?.canvasZoomUp()
}

function tapUndo() {
  fdb.value?.canvasUndo()
}

function tapRedo() {
  fdb.value?.canvasRedo()
}

function tapClear() {
  fdb.value?.canvasClear()
}

function tapSave() {
  fdb.value?.canvasExport((dataURL: any) => {
    const link = document.createElement('a')
    link.download = 'canvas.png'
    link.href = dataURL
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  })
}

function onTapButton() {
}

function tapReadonly() {
  canvasReadonly.value = !canvasReadonly.value
}
</script>

<template>
  <div class="home">
    <div style="text-align: left">
      <button @click="tapBrush(2)">
        brush1
      </button>
      <button @click="tapBrush(4)">
        brush2
      </button>
      <button @click="tapBrush(6)">
        brush3
      </button>
      <button @click="tapBrush(8)">
        brush4
      </button>
      <span style="margin-right: 50px" />
      <button @click="tapLine(2)">
        line1
      </button>
      <button @click="tapLine(4)">
        line2
      </button>
      <button @click="tapLine(6)">
        line3
      </button>
      <button @click="tapLine(8)">
        line4
      </button>
    </div>
    <div style="text-align: left">
      <button @click="tapRect(2)">
        rect1
      </button>
      <button @click="tapRect(4)">
        rect2
      </button>
      <button @click="tapRect(6)">
        rect3
      </button>
      <button @click="tapRect(8)">
        rect4
      </button>
      <span style="margin-right: 50px" />
      <button @click="tapCircle(2)">
        circle1
      </button>
      <button @click="tapCircle(4)">
        circle2
      </button>
      <button @click="tapCircle(6)">
        circle3
      </button>
      <button @click="tapCircle(8)">
        circle4
      </button>
    </div>
    <div style="text-align: left">
      <button @click="tapText(18)">
        text1
      </button>
      <button @click="tapText(24)">
        text2
      </button>
      <button @click="tapText(32)">
        text3
      </button>
      <button @click="tapText(48)">
        text4
      </button>
      <span style="margin-right: 50px" />
      <button @click="tapEraser(4)">
        eraser1
      </button>
      <button @click="tapEraser(12)">
        eraser2
      </button>
      <button @click="tapEraser(20)">
        eraser3
      </button>
      <button @click="tapEraser(28)">
        eraser4
      </button>
    </div>
    <div style="text-align: left">
      画笔颜色：
      <button @click="setBrushColor('#f00')">
        红
      </button>
      <button @click="setBrushColor('#0f0')">
        绿
      </button>
      <button @click="setBrushColor('#00f')">
        蓝
      </button>
    </div>
    <div style="text-align: left">
      线条颜色：
      <button @click="setStrokeColor('#f00')">
        红
      </button>
      <button @click="setStrokeColor('#0f0')">
        绿
      </button>
      <button @click="setStrokeColor('#00f')">
        蓝
      </button>
    </div>
    <div style="text-align: left">
      填充色颜色：
      <button @click="setFillColor('#f00')">
        红
      </button>
      <button @click="setFillColor('#0f0')">
        绿
      </button>
      <button @click="setFillColor('#00f')">
        蓝
      </button>
    </div>
    <div style="text-align: left">
      背景色颜色：
      <button @click="setBgColor('rgba(0,0,0,0)')">
        透明
      </button>
      <button @click="setBgColor('rgba(0,0,0)')">
        黑
      </button>
      <button @click="setBgColor('rgba(255,255,0)')">
        黄
      </button>
    </div>
    <div style="text-align: left">
      <button @click="tapMove">
        move
      </button>
      <button @click="tapZoomDown">
        zoom -
      </button>
      <button @click="tapZoomUp">
        zoom +
      </button>
      <button @click="tapUndo">
        undo
      </button>
      <button @click="tapRedo">
        redo
      </button>
      <button @click="tapClear">
        clear
      </button>
      <button @click="tapSave">
        save
      </button>
      <button @click="tapReadonly">
        设置为：{{ canvasReadonly ? "读写" : "只读" }}
      </button>
    </div>
    <div style="width: 100%; height: 600px; position: relative">
      <button @click="onTapButton">
        点我一下
      </button>
      <div
        style="
          width: 100%;
          position: absolute;
          left: 0;
          top: 0;
          height: 600px;
          border: 1px solid #000;
          overflow: hidden;
        "
        :style="{ pointerEvents: canvasReadonly ? 'none' : 'auto' }"
      >
        <canvas ref="myCanvasRef" />
      </div>
    </div>
  </div>
</template>

<style scoped>
button {
  min-width: 100px;
  margin-right: 10px;
  margin-bottom: 5px;
}
</style>
