<script setup lang="ts">
import { N<PERSON><PERSON><PERSON>, NCard, NTag } from 'naive-ui'
import { IconifyIcon } from '@iconify/vue'
</script>

<template>
  <div class="update-strategy-page">
    <NCard title="更新策略说明" class="strategy-card">
      <div class="strategy-content">
        <!-- 平台策略说明 -->
        <div class="platform-section">
          <h3>平台更新策略</h3>
          <div class="platform-list">
            <div class="platform-item">
              <div class="platform-header">
                <IconifyIcon icon="logos:apple" class="platform-icon" />
                <span class="platform-name">macOS</span>
                <NTag type="info" size="small">
                  全量更新
                </NTag>
              </div>
              <div class="platform-desc">
                <p>macOS 平台使用全量更新策略，每次更新都会下载完整的应用程序包。</p>
                <ul>
                  <li>更新包格式：DMG 或 ZIP</li>
                  <li>更新方式：完整替换</li>
                  <li>优点：稳定可靠，兼容性好</li>
                  <li>缺点：下载包较大，更新时间较长</li>
                </ul>
              </div>
            </div>

            <div class="platform-item">
              <div class="platform-header">
                <IconifyIcon icon="logos:microsoft-windows" class="platform-icon" />
                <span class="platform-name">Windows</span>
                <NTag type="success" size="small">
                  增量更新
                </NTag>
              </div>
              <div class="platform-desc">
                <p>Windows 平台支持增量更新策略，只下载变更的文件部分。</p>
                <ul>
                  <li>更新包格式：NSIS 差分包</li>
                  <li>更新方式：差分更新</li>
                  <li>优点：下载包小，更新速度快</li>
                  <li>缺点：需要基础版本支持</li>
                </ul>
              </div>
            </div>

            <div class="platform-item">
              <div class="platform-header">
                <IconifyIcon icon="logos:linux-tux" class="platform-icon" />
                <span class="platform-name">Linux</span>
                <NTag type="success" size="small">
                  增量更新
                </NTag>
              </div>
              <div class="platform-desc">
                <p>Linux 平台支持增量更新策略，通过差分包进行更新。</p>
                <ul>
                  <li>更新包格式：AppImage、DEB、RPM 差分包</li>
                  <li>更新方式：差分更新</li>
                  <li>优点：下载包小，更新速度快</li>
                  <li>缺点：需要基础版本支持</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- 更新流程说明 -->
        <div class="process-section">
          <h3>更新流程</h3>
          <div class="process-steps">
            <div class="step-item">
              <div class="step-number">
                1
              </div>
              <div class="step-content">
                <h4>检查更新</h4>
                <p>应用启动时自动检查更新，或用户手动触发检查</p>
              </div>
            </div>

            <div class="step-item">
              <div class="step-number">
                2
              </div>
              <div class="step-content">
                <h4>下载更新</h4>
                <p>根据平台策略下载全量包或增量包</p>
              </div>
            </div>

            <div class="step-item">
              <div class="step-number">
                3
              </div>
              <div class="step-content">
                <h4>验证更新</h4>
                <p>验证下载文件的完整性和签名</p>
              </div>
            </div>

            <div class="step-item">
              <div class="step-number">
                4
              </div>
              <div class="step-content">
                <h4>安装更新</h4>
                <p>关闭应用，安装更新，重启应用</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 配置说明 -->
        <div class="config-section">
          <h3>配置说明</h3>
          <div class="config-content">
            <div class="config-item">
              <h4>electron-builder 配置</h4>
              <div class="code-block">
                <pre><code>{
  "publish": {
    "provider": "generic",
    "url": "https://your-update-server.com/updates",
    "channel": "latest"
  },
  "nsis": {
    "differentialPackage": true  // Windows 增量更新
  },
  "generateUpdatesFilesForAllChannels": true
}</code></pre>
              </div>
            </div>

            <div class="config-item">
              <h4>更新服务器要求</h4>
              <ul>
                <li><strong>macOS:</strong> 提供 <code>latest-mac.yml</code> 元数据文件</li>
                <li><strong>Windows:</strong> 提供 <code>latest.yml</code> 元数据文件</li>
                <li><strong>Linux:</strong> 提供 <code>latest-linux.yml</code> 元数据文件</li>
                <li>支持 HTTPS 协议</li>
                <li>正确的 CORS 配置</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 注意事项 -->
        <div class="notes-section">
          <h3>注意事项</h3>
          <NAlert type="warning" title="重要提醒">
            <ul>
              <li>增量更新需要用户已安装基础版本</li>
              <li>macOS 由于系统限制，暂不支持增量更新</li>
              <li>更新过程中请勿关闭应用程序</li>
              <li>建议在网络良好的环境下进行更新</li>
              <li>更新前会自动备份重要数据</li>
            </ul>
          </NAlert>
        </div>
      </div>
    </NCard>
  </div>
</template>

<style scoped>
.update-strategy-page {
  padding: 16px;
  max-width: 1000px;
  margin: 0 auto;
}

.strategy-card {
  margin-bottom: 16px;
}

.strategy-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.platform-section h3,
.process-section h3,
.config-section h3,
.notes-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--n-text-color-1);
}

.platform-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.platform-item {
  border: 1px solid var(--n-border-color);
  border-radius: 8px;
  padding: 20px;
  background: var(--n-color-target);
}

.platform-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.platform-icon {
  font-size: 24px;
}

.platform-name {
  font-size: 16px;
  font-weight: 600;
}

.platform-desc p {
  margin: 0 0 12px 0;
  color: var(--n-text-color-2);
}

.platform-desc ul {
  margin: 0;
  padding-left: 20px;
  color: var(--n-text-color-2);
}

.platform-desc li {
  margin-bottom: 4px;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border: 1px solid var(--n-border-color);
  border-radius: 8px;
  background: var(--n-color-target);
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--n-primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.step-content p {
  margin: 0;
  font-size: 13px;
  color: var(--n-text-color-2);
  line-height: 1.5;
}

.config-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.config-item h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
}

.code-block {
  background: var(--n-color-target);
  border: 1px solid var(--n-border-color);
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
}

.code-block pre {
  margin: 0;
  font-family: var(--n-font-family-mono);
  font-size: 13px;
  line-height: 1.5;
}

.code-block code {
  color: var(--n-text-color-1);
}

.config-item ul {
  margin: 0;
  padding-left: 20px;
}

.config-item li {
  margin-bottom: 8px;
  color: var(--n-text-color-2);
}

.config-item code {
  background: var(--n-code-color);
  padding: 2px 6px;
  border-radius: 3px;
  font-family: var(--n-font-family-mono);
  font-size: 12px;
}

.notes-section :deep(.n-alert) {
  margin: 0;
}

.notes-section ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.notes-section li {
  margin-bottom: 4px;
}

@media (max-width: 768px) {
  .process-steps {
    grid-template-columns: 1fr;
  }

  .step-item {
    flex-direction: column;
    text-align: center;
  }

  .step-number {
    align-self: center;
  }
}
</style>
