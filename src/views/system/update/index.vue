<script setup lang="ts">
import { computed, ref } from 'vue'
import { NAlert, NButton, NCard, NInput, NProgress, useMessage } from 'naive-ui'
import { useUpdate } from '@/composables/use-update'
import { UpdateStatus, formatBytes, formatSpeed } from '@/utils/electron-update'
import UpdateDialog from '@/components/common/UpdateDialog.vue'

const message = useMessage()

// 使用更新 composable
const {
  updateInfo,
  currentVersion,
  showUpdateDialog,
  isChecking,
  isDownloading,
  isInstalling,
  checkForUpdates,
  downloadUpdate,
  installUpdate,
  setUpdateServerUrl,
  handleDialogDownload,
  handleDialogInstall,
  handleDialogCancel,
  getUpdateStatusText,
  isElectronEnvironment,
} = useUpdate()

// 本地状态
const serverUrl = ref('https://example.com/auto-updates')
const isUpdatingServer = ref(false)

// 计算属性
const showProgress = computed(() => {
  return updateInfo.value?.status === UpdateStatus.DOWNLOADING && updateInfo.value.progress
})

// 方法
function handleCheckUpdate() {
  checkForUpdates(false) // 非静默检查
}

function handleDownloadUpdate() {
  downloadUpdate()
}

function handleInstallUpdate() {
  installUpdate()
}

async function handleUpdateServerUrl() {
  if (!serverUrl.value.trim()) {
    message.warning('请输入有效的服务器地址')
    return
  }

  isUpdatingServer.value = true
  try {
    const success = await setUpdateServerUrl(serverUrl.value.trim())
    if (success) {
      message.success('更新服务器地址设置成功')
    }
  }
  finally {
    isUpdatingServer.value = false
  }
}

function getStatusClass() {
  if (!updateInfo.value)
    return ''

  switch (updateInfo.value.status) {
    case UpdateStatus.AVAILABLE:
      return 'status-available'
    case UpdateStatus.DOWNLOADING:
      return 'status-downloading'
    case UpdateStatus.DOWNLOADED:
      return 'status-downloaded'
    case UpdateStatus.ERROR:
      return 'status-error'
    case UpdateStatus.INSTALLING:
      return 'status-installing'
    default:
      return ''
  }
}

function getPlatformName() {
  if (!isElectronEnvironment())
    return '浏览器环境'

  const platform = navigator.platform.toLowerCase()
  if (platform.includes('mac'))
    return 'macOS'
  if (platform.includes('win'))
    return 'Windows'
  if (platform.includes('linux'))
    return 'Linux'
  return '未知平台'
}

function getUpdateStrategy() {
  const platform = getPlatformName()
  if (platform === 'macOS')
    return '全量更新'
  if (platform === 'Windows' || platform === 'Linux')
    return '增量更新'
  return '不支持更新'
}

function formatReleaseNotes(notes: string): string {
  return notes
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
}
</script>

<template>
  <div class="update-page">
    <NCard title="应用更新" class="update-card">
      <!-- 当前版本信息 -->
      <div class="version-section">
        <h3>版本信息</h3>
        <div class="version-info">
          <div class="info-item">
            <span class="label">当前版本：</span>
            <span class="value">{{ currentVersion || '获取中...' }}</span>
          </div>
          <div class="info-item">
            <span class="label">更新状态：</span>
            <span class="value" :class="getStatusClass()">{{ getUpdateStatusText() }}</span>
          </div>
          <div v-if="updateInfo?.version" class="info-item">
            <span class="label">最新版本：</span>
            <span class="value">{{ updateInfo.version }}</span>
          </div>
        </div>
      </div>

      <!-- 更新操作 -->
      <div class="action-section">
        <h3>更新操作</h3>
        <div class="action-buttons">
          <NButton
            type="primary"
            :loading="isChecking"
            :disabled="isDownloading || isInstalling"
            @click="handleCheckUpdate"
          >
            {{ isChecking ? '检查中...' : '检查更新' }}
          </NButton>

          <NButton
            v-if="updateInfo?.status === UpdateStatus.AVAILABLE"
            type="success"
            :loading="isDownloading"
            :disabled="isChecking || isInstalling"
            @click="handleDownloadUpdate"
          >
            {{ isDownloading ? '下载中...' : '下载更新' }}
          </NButton>

          <NButton
            v-if="updateInfo?.status === UpdateStatus.DOWNLOADED"
            type="warning"
            :loading="isInstalling"
            :disabled="isChecking || isDownloading"
            @click="handleInstallUpdate"
          >
            {{ isInstalling ? '安装中...' : '安装更新' }}
          </NButton>
        </div>
      </div>

      <!-- 下载进度 -->
      <div v-if="showProgress" class="progress-section">
        <h3>下载进度</h3>
        <div class="progress-container">
          <div class="progress-info">
            <span>{{ Math.round(updateInfo!.progress!.percent) }}%</span>
            <span>{{ formatSpeed(updateInfo!.progress!.bytesPerSecond) }}</span>
          </div>
          <NProgress
            type="line"
            :percentage="updateInfo!.progress!.percent"
            status="success"
          />
          <div class="download-info">
            <span>已下载: {{ formatBytes(updateInfo!.progress!.transferred) }}</span>
            <span>总大小: {{ formatBytes(updateInfo!.progress!.total) }}</span>
          </div>
        </div>
      </div>

      <!-- 更新说明 -->
      <div v-if="updateInfo?.releaseNotes" class="notes-section">
        <h3>更新说明</h3>
        <div class="release-notes" v-html="formatReleaseNotes(updateInfo.releaseNotes)" />
      </div>

      <!-- 错误信息 -->
      <div v-if="updateInfo?.error" class="error-section">
        <h3>错误信息</h3>
        <NAlert type="error" :title="updateInfo.error" />
      </div>
    </NCard>

    <!-- 更新设置 -->
    <NCard title="更新设置" class="settings-card">
      <div class="settings-section">
        <div class="setting-item">
          <div class="setting-label">
            <span>更新服务器地址</span>
            <span class="setting-desc">配置应用更新的服务器地址</span>
          </div>
          <div class="setting-control">
            <NInput
              v-model:value="serverUrl"
              placeholder="请输入更新服务器地址"
              :disabled="isUpdatingServer"
            />
            <NButton
              type="primary"
              :loading="isUpdatingServer"
              @click="handleUpdateServerUrl"
            >
              保存
            </NButton>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <span>平台信息</span>
            <span class="setting-desc">当前运行平台的更新策略</span>
          </div>
          <div class="setting-value">
            <div class="platform-info">
              <div class="info-row">
                <span class="label">操作系统：</span>
                <span class="value">{{ getPlatformName() }}</span>
              </div>
              <div class="info-row">
                <span class="label">更新策略：</span>
                <span class="value">{{ getUpdateStrategy() }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </NCard>

    <!-- 更新对话框 -->
    <UpdateDialog
      v-model:show="showUpdateDialog"
      :update-info="updateInfo"
      :current-version="currentVersion"
      @download="handleDialogDownload"
      @install="handleDialogInstall"
      @cancel="handleDialogCancel"
    />
  </div>
</template>

<style scoped>
.update-page {
  padding: 16px;
  max-width: 800px;
  margin: 0 auto;
}

.update-card,
.settings-card {
  margin-bottom: 16px;
}

.version-section,
.action-section,
.progress-section,
.notes-section,
.error-section,
.settings-section {
  margin-bottom: 24px;
}

.version-section h3,
.action-section h3,
.progress-section h3,
.notes-section h3,
.error-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.version-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item .label {
  min-width: 80px;
  font-weight: 500;
  color: var(--n-text-color-2);
}

.info-item .value {
  font-weight: 600;
}

.status-available {
  color: var(--n-success-color);
}

.status-downloading {
  color: var(--n-info-color);
}

.status-downloaded {
  color: var(--n-warning-color);
}

.status-error {
  color: var(--n-error-color);
}

.status-installing {
  color: var(--n-info-color);
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.progress-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

.download-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--n-text-color-2);
}

.release-notes {
  padding: 16px;
  background-color: var(--n-color-target);
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.6;
  max-height: 300px;
  overflow-y: auto;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 0;
  border-bottom: 1px solid var(--n-divider-color);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.setting-label span:first-child {
  font-weight: 500;
  font-size: 14px;
}

.setting-desc {
  font-size: 12px;
  color: var(--n-text-color-2);
}

.setting-control {
  display: flex;
  gap: 8px;
  align-items: center;
  min-width: 300px;
}

.setting-value {
  min-width: 200px;
}

.platform-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-row {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.info-row .label {
  min-width: 80px;
  color: var(--n-text-color-2);
}

.info-row .value {
  font-weight: 500;
}

:deep(code) {
  background-color: var(--n-code-color);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: var(--n-font-family-mono);
  font-size: 12px;
}
</style>
