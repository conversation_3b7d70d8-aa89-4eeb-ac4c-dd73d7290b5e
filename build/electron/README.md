# 构建资源文件说明

这个文件夹包含了electron-builder构建应用程序所需的资源文件。

## 图标文件

您需要准备以下图标文件：

### macOS
- `icon.icns` - macOS应用程序图标
  - 建议尺寸：512x512px 或更高
  - 格式：ICNS格式

### Windows
- `icon.ico` - Windows应用程序图标
  - 建议尺寸：256x256px 或更高
  - 格式：ICO格式，包含多种尺寸（16x16, 32x32, 48x48, 64x64, 128x128, 256x256）

### Linux
- `icon.png` - Linux应用程序图标
  - 建议尺寸：512x512px
  - 格式：PNG格式

## 图标制作工具推荐

1. **在线工具**：
   - [IconGenerator](https://icongenerator.net/) - 从PNG生成各平台图标
   - [CloudConvert](https://cloudconvert.com/) - 格式转换

2. **本地工具**：
   - macOS: `iconutil` 命令行工具
   - Windows: IcoFX, Greenfish Icon Editor Pro
   - 跨平台: GIMP, Photoshop

## 配置文件说明

- `entitlements.mac.plist` - macOS应用程序权限配置
- `installer.nsh` - Windows NSIS安装程序自定义脚本

## 构建命令

```bash
# 开发环境构建（不发布）
pnpm run build-electron:dev

# 生产环境构建并发布
pnpm run build-electron

# 仅构建到目录（用于测试）
pnpm run build-electron:dir
```

## 注意事项

1. 确保所有图标文件都已准备好再进行构建
2. 如果需要代码签名，请配置相应的证书
3. 发布到GitHub需要配置GitHub token
4. 根据实际需求修改`package.json`中的发布配置
